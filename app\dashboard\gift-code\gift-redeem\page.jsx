"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-toastify";

const page = () => {
  const [giftCode, setGiftCode] = useState("");
  const router = useRouter();

  const handleRedeemGiftCode = async (e) => {
    e.preventDefault();
    try {
      const requestBody = {
        code: giftCode,
      };
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/gifts/redeem`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      console.log(res.data);
      toast.success("Gift code redeemed successfully!");
      router.push("/dashboard/gift-code/my-redeem-history");
      setGiftCode("");
    } catch (e) {
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
      }
      console.log(e.response.data);
    }
  };

  return (
    <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
      <form onSubmit={handleRedeemGiftCode}>
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">
            Gift Code
          </label>
          <input
            className="border rounded w-full py-2 px-3 text-gray-700"
            type="text"
            placeholder="Enter Gift Code"
            value={giftCode}
            onChange={(e) => setGiftCode(e.target.value)}
          />
        </div>
        <div className="flex justify-center">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
            type="submit"
          >
            Redeem
          </button>
        </div>
      </form>
    </div>
  );
};

export default page;
