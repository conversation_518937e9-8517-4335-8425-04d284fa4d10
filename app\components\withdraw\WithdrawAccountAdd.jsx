"use client";
import axios from "axios";
import Cookies from "js-cookie";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const WithdrawAccountAdd = () => {
  const [wallets, setWallets] = useState([]);
  const [walletsLoading, setWalletsLoading] = useState(false);
  const [selectWallet, setSelectWallet] = useState("");
  const [withdrawMethod, setWithdrawMethod] = useState([]);
  const [methodsLoading, setMethodsLoading] = useState(false);
  const [selectMethodId, setSelectMethodId] = useState("");
  const [selectMethod, setSelectMethod] = useState(null);
  const [manualFields, setManualFields] = useState({});
  const router = useRouter();

  // get wallets data
  const walletsData = async () => {
    try {
      setWalletsLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setWallets(res.data.data.wallets);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setWalletsLoading(false);
    }
  };

  // get withdraw method data
  const withdrawMethodData = async () => {
    try {
      setMethodsLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts/methods/list`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
          params: { currency: selectWallet },
        }
      );
      setWithdrawMethod(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setMethodsLoading(false);
    }
  };

  // Handle method change
  const handleMethodChange = (e) => {
    const selectedId = e.target.value;
    setSelectMethodId(selectedId);

    const selected = withdrawMethod.find((m) => m.id === parseInt(selectedId));
    setSelectMethod(selected || null);
  };

  // Handle manual field change
  const handleManualFieldChange = (fieldName, value) => {
    setManualFields((prev) => ({ ...prev, [fieldName]: value }));
  };

  // Handle create withdraw account
  const handleCreateWithdrawAccount = async () => {
    try {
      if (!selectWallet || !selectMethod) {
        toast.error("Please select wallet and method");
        return;
      }
      if (Object.keys(manualFields).length === 0) {
        toast.error("Please fill all required fields");
        return;
      }

      const formData = new FormData();

      // basic fields
      formData.append(
        "wallet_id",
        selectWallet === "default" ? "default" : selectWallet
      );
      formData.append("withdraw_method_id", selectMethod.id);
      formData.append("method_name", selectMethod.name);

      // append dynamic manual fields as credentials[key]
      Object.entries(manualFields).forEach(([key, value]) => {
        if (value instanceof File) {
          formData.append(`credentials[${key}]`, value);
        } else {
          formData.append(`credentials[${key}]`, value);
        }
      });

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );
      toast.success("Withdraw account created successfully!");
      router.push("/dashboard/withdraw/withdraw-account");
    } catch (e) {
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
      } else {
        toast.error("Something went wrong, please try again.");
      }
    }
  };

  useEffect(() => {
    walletsData();
  }, []);

  useEffect(() => {
    if (selectWallet) {
      withdrawMethodData();
      setSelectMethodId("");
      setSelectMethod(null);
    }
  }, [selectWallet]);

  return (
    <div>
      <Link
        href="/dashboard/withdraw/withdraw-account"
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 inline-flex items-center space-x-2"
      >
        <span className="text-sm">Back to withdraw account</span>
      </Link>

      <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Wallet
            </label>
            <select
              className="border rounded w-full py-2 px-3 text-gray-700"
              disabled={walletsLoading}
              value={selectWallet}
              onChange={(e) => setSelectWallet(e.target.value)}
            >
              <option value="">
                {walletsLoading ? "Loading wallets..." : "Select Wallet"}
              </option>
              {wallets.map((wallet) => (
                <option
                  key={wallet.code}
                  value={wallet.is_default ? "default" : wallet.id}
                >
                  {wallet.name}
                </option>
              ))}
            </select>
          </div>

          <div className="col-span-12">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Withdraw Method
            </label>
            <select
              className="border rounded w-full py-2 px-3 text-gray-700"
              disabled={methodsLoading}
              value={selectMethodId}
              onChange={handleMethodChange}
            >
              <option value="">
                {methodsLoading ? "Loading Methods..." : "Select Method"}
              </option>
              {withdrawMethod.map((method) => (
                <option key={method.id} value={method.id}>
                  {method.name}
                </option>
              ))}
            </select>
          </div>

          {/* Manual Method Fields */}
          {selectMethod && selectMethod.type === "manual" && (
            <>
              <div className="col-span-12">
                {selectMethod?.fields?.map((field, index) => (
                  <div key={index} className="mb-4">
                    <label className="block text-gray-700 text-sm font-bold mb-2">
                      {field.name}
                    </label>

                    {field.type === "textarea" ? (
                      <textarea
                        className="border rounded w-full py-2 px-3 text-gray-700"
                        placeholder={field.name}
                        value={manualFields[field.name] || ""}
                        onChange={(e) =>
                          handleManualFieldChange(field.name, e.target.value)
                        }
                      />
                    ) : field.type === "file" ? (
                      <input
                        type="file"
                        className="block w-full text-sm text-gray-700 border border-gray-300 rounded"
                        onChange={(e) =>
                          handleManualFieldChange(field.name, e.target.files[0])
                        }
                      />
                    ) : (
                      <input
                        className="border rounded w-full py-2 px-3 text-gray-700"
                        type={field.type}
                        placeholder={field.name}
                        value={manualFields[field.name] || ""}
                        onChange={(e) =>
                          handleManualFieldChange(field.name, e.target.value)
                        }
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="col-span-12">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
                  onClick={handleCreateWithdrawAccount}
                >
                  Create
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default WithdrawAccountAdd;
