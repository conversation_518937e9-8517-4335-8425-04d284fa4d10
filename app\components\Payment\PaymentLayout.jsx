"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import MakePayment from "./MakePayment/MakePayment";
import PaymentHistory from "./PaymentHistory/PaymentHistory";

const PaymentLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };
  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/add-money/add"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/add-money/add")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Add Money
        </Link>
        <Link
          href="/dashboard/add-money/history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/add-money/history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          History
        </Link>
      </div>
      <div>
        <MakePayment></MakePayment>
        <PaymentHistory></PaymentHistory>
      </div>
    </div>
  );
};

export default PaymentLayout;
