"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

const CreateInvoiceLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };

  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/create-invoice/create"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/create-invoice/create")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Create Invoice
        </Link>
        <Link
          href="/dashboard/create-invoice/history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/create-invoice/history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Invoice History
        </Link>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default CreateInvoiceLayout;
