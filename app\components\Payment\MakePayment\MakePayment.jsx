"use client";

import Link from "next/link";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter, usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { dynamicDecimals } from "@/utils";

const MakePayment = () => {
  // fetch state
  const [wallets, setWallets] = useState([]);
  const [paymentSettings, setPaymentSettings] = useState([]);
  console.log("paymentSettings", paymentSettings);

  // form state
  const [merchantMID, setMerchantMID] = useState("");
  const [selectWallet, setSelectWallet] = useState("");
  const [selectAmount, setSelectAmount] = useState("");
  const [paymentResponse, setPaymentResponse] = useState(null);

  // charge calculation states
  const [calculatedCharge, setCalculatedCharge] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);

  // others state
  const router = useRouter();
  const [step, setStep] = useState(1);

  // Fetch wallet data
  const makePaymentData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            payment: 1
          }
        }
      );
      setWallets(res.data.data.wallets);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  // Fetch payment setting
  const fetchPaymentSettings = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/payment/settings`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setPaymentSettings(res.data.data);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  // submit API call
  const submitMakePayment = async () => {
    try {
      // find selected wallet
      const selectedWalletObj = wallets.find(
        (w) => w.id === Number(selectWallet)
      );

      const requestBody = {
        merchant_number: merchantMID,
        wallet_id: selectedWalletObj?.is_default ? "default" : selectWallet,
        amount: selectAmount,
      };

      console.log("Request Body:", requestBody);

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/payment/make`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success(res.data.message);
      console.log("Payment Response:", res.data);
      setPaymentResponse(res.data);
    } catch (err) {
      console.error("API Error:", err);

      if (err.response) {
        // Server error (400, 401, 422, 500)
        const status = err.response.status;
        const message =
          err.response.data?.message || `Request failed with status ${status}`;

        toast.error(message);

        // unauthorized হলে login এ redirect
        if (status === 401) {
          Cookies.remove("token");
          router.push("/auth/login");
        }
      } else if (err.request) {
        toast.error("No response from server. Please try again later.");
      } else {
        toast.error("Unexpected error: " + err.message);
      }
    }
  };

  useEffect(() => {
    makePaymentData();
    fetchPaymentSettings();
  }, []);

  // charge amount calculate
  useEffect(() => {
    if (selectAmount && paymentSettings.user_charge_type) {
      const baseAmount = parseFloat(selectAmount);
      let chargeValue = 0;

      const chargeType = paymentSettings.user_charge_type;
      const chargeAmount = paymentSettings.user_charge;

      if (chargeType?.toLowerCase() === "percentage") {
        chargeValue = (baseAmount * parseFloat(chargeAmount)) / 100;
      } else if (chargeType?.toLowerCase() === "fixed") {
        chargeValue = parseFloat(chargeAmount);
      }

      const total = baseAmount + chargeValue;
      console.log("is loaded");

      setCalculatedCharge(chargeValue);
      setTotalAmount(total);
    }
  }, [selectAmount, paymentSettings]);

  // validation
  const handleValidated = () => {
    if (merchantMID === "") {
      toast.error("Please enter a merchant MID");
      return false;
    }
    if (selectWallet === "") {
      toast.error("Please select wallet");
      return false;
    }

    const amount = Number(selectAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    const wallet = wallets.find((w) => w.id === Number(selectWallet));
    if (wallet) {
      // clean numbers (remove commas if any)
      const min = parseFloat(wallet.payment_limit.min.replace(/,/g, ""));
      const max = parseFloat(wallet.payment_limit.max.replace(/,/g, ""));

      // FIX: siteCurrencyCode/siteCurrencyDecimals from paymentSettings
      const siteCurrencyCode = paymentSettings?.site_currency_code || "USD"; // fallback
      const siteCurrencyDecimals = paymentSettings?.site_currency_decimals || 2;

      // use helper
      const decimals = dynamicDecimals({
        currencyCode: wallet.code,
        siteCurrencyCode,
        siteCurrencyDecimals,
        isCrypto: wallet.is_crypto,
      });

      if (amount < min || amount > max) {
        toast.error(
          `Amount must be between ${min.toFixed(decimals)} and ${max.toFixed(
            decimals
          )} ${wallet.code}`
        );
        return false;
      }
    }

    return true;
  };

  // Step navigation
  const nextStep = () => {
    if (!handleValidated()) return;
    setStep((prev) => Math.min(prev + 1, 3));
  };
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));
  const resetSteps = () => setStep(1);

  const selectedWallet = wallets.find((w) => w.id === Number(selectWallet));

  return (
    <>
      <h2 className="text-3xl font-bold mb-5">Make Payment</h2>
      {/* Step 1 - Form */}
      {step === 1 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <div className="grid grid-cols-12 gap-5">
            {/* Merchant MID */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Merchant MID
              </label>
              <input
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="text"
                value={merchantMID}
                onChange={(e) => setMerchantMID(e.target.value)}
              />
            </div>

            {/* Wallet */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Wallet
              </label>
              <select
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value={selectWallet}
                onChange={(e) => setSelectWallet(e.target.value)}
              >
                <option value="">Select Wallet</option>
                {wallets.map((wallet) => (
                  <option key={wallet.id} value={wallet.id}>
                    {wallet.full_name_balance}
                  </option>
                ))}
              </select>
            </div>

            {/* Amount */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Amount
              </label>
              <input
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="number"
                value={selectAmount}
                onChange={(e) => setSelectAmount(e.target.value)}
              />
              {selectWallet && (
                <p className="text-sm text-red-600 mt-1">
                  {
                    wallets.find((w) => w.id === Number(selectWallet))
                      ?.payment_limit_text
                  }
                </p>
              )}
            </div>

            {/* Next button */}
            <div className="col-span-12 flex justify-end">
              <button
                onClick={nextStep}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 w-full px-4 rounded"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2 - Confirmation */}
      {step === 2 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <h3 className="text-2xl border-b border-gray-300 font-semibold mb-5 pb-3">
            Confirm Payment
          </h3>
          <ul className="flex gap-4 flex-col mb-6 text-gray-700">
            <li className="flex justify-between">
              <strong>Amount:</strong> {selectAmount} {selectedWallet?.code}
            </li>
            <li className="flex justify-between">
              <strong>Wallet:</strong> {selectedWallet?.full_name}
            </li>
            <li className="flex justify-between">
              <strong>Merchant MID:</strong> {merchantMID}
            </li>
            <li className="flex justify-between">
              <strong>Charge Type:</strong> {paymentSettings.user_charge_type}
            </li>
            <li className="flex justify-between">
              <strong>Charge Rate:</strong> {paymentSettings.user_charge}{" "}
              {paymentSettings.user_charge_type === "percentage"
                ? "%"
                : selectedWallet?.code}
            </li>
            <li className="flex justify-between">
              <strong>Charge Amount:</strong>{" "}
              {calculatedCharge.toFixed(
                dynamicDecimals({
                  currencyCode: selectedWallet?.code,
                  siteCurrencyCode:
                    paymentSettings?.site_currency_code || "USD",
                  siteCurrencyDecimals:
                    paymentSettings?.site_currency_decimals || 2,
                  isCrypto: selectedWallet?.is_crypto || false,
                })
              )}{" "}
              {selectedWallet?.code}
            </li>
            <li className="flex justify-between">
              <strong>Total Amount:</strong>{" "}
              {totalAmount.toFixed(
                dynamicDecimals({
                  currencyCode: selectedWallet?.code,
                  siteCurrencyCode:
                    paymentSettings?.site_currency_code || "USD",
                  siteCurrencyDecimals:
                    paymentSettings?.site_currency_decimals || 2,
                  isCrypto: selectedWallet?.is_crypto || false,
                })
              )}{" "}
              {selectedWallet?.code}
            </li>
          </ul>

          <div className="flex gap-3">
            <button
              onClick={prevStep}
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded w-full"
            >
              Back
            </button>
            <button
              onClick={async () => {
                await submitMakePayment();
                setStep(3);
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded w-full"
            >
              Confirm & Proceed
            </button>
          </div>
        </div>
      )}

      {/* Step 3 - Success */}
      {step === 3 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
          <h3 className="text-3xl font-semibold text-green-600 mb-4">
            {paymentResponse.data.message}
          </h3>
          <div className="grid grid-cols-12 gap-6 mb-5">
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Transaction ID</p>
                <p>{paymentResponse?.data.transaction.tnx}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Charge</p>
                <p>{paymentResponse?.data.transaction.charge}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Merchant</p>
                <p>{paymentResponse?.data.transaction.created_at}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Final Amount</p>
                <p>{paymentResponse?.data.transaction.amount}</p>
              </div>
            </div>
          </div>
          <button
            onClick={() => {
              resetSteps();
              setPaymentResponse(null); // reset response for next payment
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          >
            Make Another Payment
          </button>
        </div>
      )}
    </>
  );
};

export default MakePayment;
