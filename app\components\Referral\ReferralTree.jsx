"use client";

import React, { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

const ReferralTreeNode = ({ node }) => {
  if (!node) return null;

  return (
    <li className={node.is_me ? "" : "child"}>
      {node.is_me ? (
        //Parent node layout 
        <div className=" referral-tree-item-parent">
          <div className="referral-tree-card tree-parent">
            <div className="thumb">
              <img
                src={node.avatar || "/default-avatar.png"}
                alt={node.name || "User"}
              />
            </div>
            <div className="content">
              <h5 className="title">Me ({node.name})</h5>
            </div>
          </div>

          {node.children && node.children.length > 0 && (
            <ul>
              {node.children.map((child) => (
                <ReferralTreeNode
                  key={child.id || Math.random()}
                  node={child}
                />
              ))}
            </ul>
          )}
        </div>
      ) : (
        // Child nodes layout (same as before)
        <div className="referral-tree-item tree-children">
          <div className="referral-tree-card-inner">
            <div className="referral-tree-card">
              <div className="thumb">
                <img
                  src={node.avatar || "/default-avatar.png"}
                  alt={node.name || "User"}
                />
              </div>
              <div className="content">
                <h5 className="title">{node.name}</h5>
                <p className="info">Deposit 50, Invest $0, ROI Profit $0</p>
              </div>
            </div>
          </div>

          {node.children && node.children.length > 0 && (
            <ul>
              {node.children.map((child) => (
                <ReferralTreeNode
                  key={child.id || Math.random()}
                  node={child}
                />
              ))}
            </ul>
          )}
        </div>
      )}
    </li>
  );
};

const ReferralTree = () => {
  const router = useRouter();
  const [treeData, setTreeData] = useState(null);

  useEffect(() => {
    const fetchTreeData = async () => {
      try {
        const res = await axios.get(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/referral/tree`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${Cookies.get("token")}`,
            },
          }
        );
        setTreeData(res.data.data);
      } catch (err) {
        console.log("API Error", err);
        if (err.response?.status === 401) {
          Cookies.remove("token");
          router.push("/auth/login");
        }
      }
    };
    fetchTreeData();
  }, [router]);

  return (
    <div className="min-h-screen rounded-xl bg-gray-900 text-white pt-3 p-6">
      <h3 className="text-2xl md:text-3xl font-bold mb-8">Referral Tree</h3>
      {treeData ? (
        <div className="referral-tree-main overflow-x-auto">
          <div className="td-referral-tree">
            <ul>
              <ReferralTreeNode node={treeData} />
            </ul>
          </div>
        </div>
      ) : (
        <p className="text-center">Loading tree...</p>
      )}
    </div>
  );
};

export default ReferralTree;
