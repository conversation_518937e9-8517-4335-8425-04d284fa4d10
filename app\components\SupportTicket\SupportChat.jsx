"use client";
import Link from "next/link";
import React, { useState, useRef } from "react";
import { Paperclip } from "lucide-react";

function SupportChat() {
  const [message, setMessage] = useState("");
  const [attachedFiles, setAttachedFiles] = useState([]);
  const textareaRef = useRef(null);
  const fileInputRef = useRef(null);

  // Auto-resize textarea
  const handleTextareaChange = (e) => {
    setMessage(e.target.value);
    const textarea = e.target;
    textarea.style.height = "auto";
    textarea.style.height = Math.min(textarea.scrollHeight, 200) + "px";
  };

  // Handle file attachment
  const handleAttachment = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setAttachedFiles((prev) => [...prev, ...files]);
      console.log(
        "Selected files:",
        files.map((f) => f.name)
      );
    }
  };

  const messages = [
    {
      id: 1,
      user: "Demo User",
      email: "<EMAIL>",
      message: "hello shohag",
      timestamp: "2025-08-28 06:33:18",
      attachments: [
        {
          name: "NIMhhTZ4tUhMtCSwEf4S.png",
          type: "image",
        },
      ],
      isAdmin: false,
    },
  ];

  return (
    <>
      <div className="min-h-screen">
        {/* Header */}
        <div className="inline-flex items-center justify-between mb-6 w-full">
          <h3 className="text-2xl md:text-3xl font-bold">Reply Ticket</h3>
          <Link
            href="/dashboard/support"
            className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all"
          >
            Mark as closed
          </Link>
        </div>

        <div className="bg-gray-900 p-6 min-h-screen rounded-lg">
          <div className="bg-gray-900 text-gray-200">
            <div className="flex gap-y-8 flex-col">
              {/* Messages Area */}
              <div className="flex-1 p-5 overflow-y-auto">
                <div className="space-y-6 h-[calc(100vh-300px)]">
                  {messages.map((msg) => (
                    <div
                      key={msg.id}
                      className="flex flex-col justify-between gap-3"
                    >
                      <div className="flex-1">
                        <div className="flex gap-2">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                              </div>
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            <div className="text-white font-medium text-sm">
                              {msg.user}
                            </div>
                            <div className="text-gray-400 text-xs">
                              {msg.email}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="bg-slate-700 rounded-lg space-x-4 p-4">
                        {/* Attachments */}
                        {msg.attachments && msg.attachments.length > 0 && (
                          <div className="mb-3">
                            <div className="text-green-400 text-sm font-medium mb-2">
                              Attachments
                            </div>
                            {msg.attachments.map((attachment, index) => (
                              <div
                                key={index}
                                className="flex items-center space-x-2 text-gray-300"
                              >
                                <Paperclip className="w-4 h-4 text-pink-400" />
                                <span className="text-sm">
                                  {attachment.name}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Timestamp */}
                        <div className="text-gray-400 text-xs">
                          {msg.timestamp}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Input Container */}
              <div className="bg-gray-800 border-gray-700 p-5 rounded-lg">
                <div className="bg-gray-800 border border-gray-600 rounded-xl min-h-[120px] flex flex-col relative">
                  {/* Text Input Area */}
                  <div className="flex-1 relative">
                    <textarea
                      ref={textareaRef}
                      value={message}
                      onChange={handleTextareaChange}
                      className="bg-transparent border-none text-gray-200 text-base font-sans p-4 pr-14 resize-none w-full min-h-[80px] outline-none placeholder-gray-500"
                      placeholder="Write your reply..."
                      rows="3"
                    />
                  </div>

                  {/* Attached Files Display */}
                  {attachedFiles.length > 0 && (
                    <div className="px-4 py-2 border-t border-gray-600">
                      <div className="flex flex-wrap gap-2">
                        {attachedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="bg-gray-700 px-2 py-1 rounded text-xs flex items-center gap-1"
                          >
                            <span>{file.name}</span>
                            <button
                              onClick={() =>
                                setAttachedFiles((prev) =>
                                  prev.filter((_, i) => i !== index)
                                )
                              }
                              className="text-red-400 hover:text-red-300 ml-1"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Button Container */}
                  <div className="flex justify-end items-center p-4 border-t border-gray-600 gap-3">
                    {/* Add Attachment Button */}
                    <button
                      onClick={handleAttachment}
                      className="bg-gray-600 hover:bg-gray-500 text-gray-200 px-4 py-2.5 rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 flex items-center gap-2 w-full md:w-auto justify-center"
                    >
                      <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24">
                        <path d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66L9.64 16.2a2 2 0 01-2.83-2.83l8.49-8.49" />
                      </svg>
                      Add Attachment
                    </button>

                    {/* Send Button */}
                    <button
                      disabled={!message.trim() && attachedFiles.length === 0}
                      className="bg-gradient-to-br from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white px-5 py-2.5 rounded-lg text-sm font-semibold cursor-pointer transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0 disabled:hover:translate-y-0 flex items-center gap-2 shadow-lg shadow-violet-500/30 hover:shadow-violet-500/40 disabled:shadow-none w-full md:w-auto justify-center"
                    >
                      <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
                      </svg>
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
        </div>
      </div>
    </>
  );
}

export default SupportChat;
