"use client";

import Link from "next/link";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter, usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { dynamicDecimals } from "@/utils";

const TransferMoney = () => {
  // fetch state
  const [wallets, setWallets] = useState([]);
  const [configData, setConfigData] = useState([]);

  // form state
  const [selectWallet, setSelectWallet] = useState("");
  const [recipientUID, setRecipientUID] = useState("");
  const [selectAmount, setSelectAmount] = useState("");
  const [transferResponse, setTransferResponse] = useState(null);

  // charge calculation states
  const [calculatedCharge, setCalculatedCharge] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);

  // others state
  const router = useRouter();
  const [step, setStep] = useState(1);

  // Fetch wallet data
  const fetchWalletData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setWallets(res.data.data.wallets);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  // Fetch config data
  const fetchConfigData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/transfer/config`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setConfigData(res.data.data);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  console.log("Config Data", configData);
  console.log("transferResponse", transferResponse);

  // submit API call
  const submitFoundTransfer = async () => {
    try {
      const selectedWalletObj = wallets.find(
        (w) => w.id === Number(selectWallet)
      );

      const requestBody = {
        account_number: recipientUID,
        wallet_id: selectedWalletObj?.is_default ? "default" : selectWallet,
        amount: selectAmount,
      };

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/transfer`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success(res.data.message);
      setTransferResponse(res.data);
      return true; // true
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again.");
      }
      return false; // false
    }
  };

  useEffect(() => {
    fetchWalletData();
    fetchConfigData();
  }, []);

  // charge amount calculate
  useEffect(() => {
    if (selectAmount && configData?.settings) {
      const baseAmount = parseFloat(selectAmount);
      let chargeValue = 0;

      const chargeType = configData.settings.charge_type;
      const chargeAmount = parseFloat(configData.settings.charge);

      if (chargeType?.toLowerCase() === "percentage") {
        chargeValue = (baseAmount * chargeAmount) / 100;
      } else if (chargeType?.toLowerCase() === "fixed") {
        chargeValue = chargeAmount;
      }

      const total = baseAmount + chargeValue;

      setCalculatedCharge(chargeValue);
      setTotalAmount(total);
    }
  }, [selectAmount, configData]);

  // validation
  const handleValidated = () => {
    if (recipientUID === "") {
      toast.error("Please enter a Recipient UID");
      return false;
    }
    if (selectWallet === "") {
      toast.error("Please select wallet");
      return false;
    }

    const amount = Number(selectAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    const wallet = wallets.find((w) => w.id === Number(selectWallet));
    if (wallet) {
      // clean numbers (remove commas if any)
      const min = parseFloat(wallet.transfer_limit.min.replace(/,/g, ""));
      const max = parseFloat(wallet.transfer_limit.max.replace(/,/g, ""));

      // FIX: siteCurrencyCode/siteCurrencyDecimals from configData
      const siteCurrencyCode = configData?.site_currency_code || "USD"; // fallback
      const siteCurrencyDecimals = configData?.site_currency_decimals || 2;

      // use helper
      const decimals = dynamicDecimals({
        currencyCode: wallet.code,
        siteCurrencyCode,
        siteCurrencyDecimals,
        isCrypto: wallet.is_crypto,
      });

      if (amount < min || amount > max) {
        toast.error(
          `Amount must be between ${min.toFixed(decimals)} and ${max.toFixed(
            decimals
          )} ${wallet.code}`
        );
        return false;
      }
    }

    return true;
  };

  // Step navigation
  const nextStep = () => {
    if (!handleValidated()) return;
    setStep((prev) => Math.min(prev + 1, 3));
  };
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));
  const resetSteps = () => setStep(1);

  const selectedWallet = wallets.find((w) => w.id === Number(selectWallet));

  return (
    <>
      {/* Step 1 - Form */}
      {step === 1 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <div className="grid grid-cols-12 gap-5">
            {/* Wallet */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Wallet
              </label>
              <select
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value={selectWallet}
                onChange={(e) => setSelectWallet(e.target.value)}
              >
                <option value="">Select Wallet</option>
                {wallets.map((wallet) => (
                  <option key={wallet.id} value={wallet.id}>
                    {wallet.full_name_balance}
                  </option>
                ))}
              </select>
            </div>

            {/* Recipient UID */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Recipient UID
              </label>
              <input
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="text"
                value={recipientUID}
                onChange={(e) => setRecipientUID(e.target.value)}
              />
            </div>

            {/* Amount */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Amount
              </label>
              <input
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="number"
                value={selectAmount}
                onChange={(e) => setSelectAmount(e.target.value)}
              />
              {selectWallet && (
                <p className="text-sm text-red-600 mt-1">
                  {
                    wallets.find((w) => w.id === Number(selectWallet))
                      ?.transfer_limit_text
                  }
                </p>
              )}
            </div>

            {/* Next button */}
            <div className="col-span-12 flex justify-end">
              <button
                onClick={nextStep}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 w-full px-4 rounded"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2 - Confirmation */}
      {step === 2 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <h3 className="text-2xl border-b border-gray-300 font-semibold mb-5 pb-3">
            Confirm Payment
          </h3>
          <ul className="flex gap-4 flex-col mb-6 text-gray-700">
            <li className="flex justify-between">
              <strong>Amount:</strong> {selectAmount} {selectedWallet?.code}
            </li>
            <li className="flex justify-between">
              <strong>Wallet:</strong> {selectedWallet?.full_name}
            </li>
            <li className="flex justify-between">
              <strong>Merchant MID:</strong> {recipientUID}
            </li>
            <li className="flex justify-between">
              <strong>Charge Type:</strong> {configData?.settings.charge_type}
            </li>
            <li className="flex justify-between">
              <strong>Charge Rate:</strong> {configData?.settings.charge}
            </li>
            <li className="flex justify-between">
              <strong>Charge Amount:</strong>{" "}
              {calculatedCharge.toFixed(
                dynamicDecimals({
                  currencyCode: selectedWallet?.code,
                  siteCurrencyCode: configData?.site_currency_code || "USD",
                  siteCurrencyDecimals: configData?.site_currency_decimals || 2,
                  isCrypto: selectedWallet?.is_crypto || false,
                })
              )}{" "}
              {selectedWallet?.code}
            </li>
            <li className="flex justify-between">
              <strong>Total Amount:</strong>{" "}
              {totalAmount.toFixed(
                dynamicDecimals({
                  currencyCode: selectedWallet?.code,
                  siteCurrencyCode: configData?.site_currency_code || "USD",
                  siteCurrencyDecimals: configData?.site_currency_decimals || 2,
                  isCrypto: selectedWallet?.is_crypto || false,
                })
              )}{" "}
              {selectedWallet?.code}
            </li>
          </ul>

          <div className="flex gap-3">
            <button
              onClick={prevStep}
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded w-full"
            >
              Back
            </button>
            <button
              onClick={async () => {
                const success = await submitFoundTransfer();
                if (success) {
                  setStep(3); // Step 3
                }
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded w-full"
            >
              Confirm & Proceed
            </button>
          </div>
        </div>
      )}

      {/* Step 3 - Success */}
      {step === 3 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
          <h3 className="text-3xl font-semibold text-green-600 mb-4">
            {transferResponse?.message}
          </h3>
          <div className="mb-5">
            <span>Sent Amount</span>
            <h3 className="text-3xl font-bold">
              {transferResponse?.data.sender_transaction.final_amount}
            </h3>
          </div>
          <div className="grid grid-cols-12 gap-6 mb-5">
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Charge</p>
                <p>{transferResponse?.data.sender_transaction.charge}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Date & Time</p>
                <p>{transferResponse?.data.sender_transaction.created_at}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Transaction ID</p>
                <p>{transferResponse?.data.sender_transaction.tnx}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Receiver name</p>
                <p>{transferResponse?.data.sender_transaction.description}</p>
              </div>
            </div>
          </div>
          <button
            onClick={() => {
              resetSteps();
              setTransferResponse(null); // reset response for next payment
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          >
            Make Another Payment
          </button>
        </div>
      )}
    </>
  );
};

export default TransferMoney;
