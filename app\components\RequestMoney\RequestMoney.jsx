"use client";

import React, { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import { dynamicDecimals } from "@/utils";

function RequestMoney() {
  // fetch state
  const [wallets, setWallets] = useState([]);
  const [paymentSettings, setPaymentSettings] = useState([]);

  const [requestMoney, setRequestMoney] = useState(null);

  // form state
  const [recipientUID, setRecipientUID] = useState("");
  const [requestAmount, setRequestAmount] = useState("");
  const [selectNote, setSelectNote] = useState("");
  const [selectedWallet, setSelectedWallet] = useState("");

  // others state
  const [step, setStep] = useState(1);

  // Fetch wallets
  const fetchWallets = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setWallets(res.data.data.wallets);
    } catch (err) {
      console.error("API Error:", err);
    }
  };

  // Fetch payment settings
  const fetchPaymentSettings = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/payment/settings`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setPaymentSettings(res.data.data);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  // Send request money API
  const fetchRequestMoney = async () => {
    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/request-money`,
        {
          wallet_id: Number(selectedWallet),
          request_to: recipientUID,
          amount: requestAmount,
          note: selectNote,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      setRequestMoney(res.data.data);
      toast.success("Request money sent successfully!");
      setStep(3); // move to success screen if you want
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again.");
      }
    }
  };

  // console.log(wallet_id);

  // Only fetch wallets + settings on mount
  useEffect(() => {
    fetchWallets();
    fetchPaymentSettings();
  }, []);

  // Step navigation
  const nextStep = () => {
    if (!handleValidated()) return;
    setStep((prev) => Math.min(prev + 1, 3));
  };
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));
  const resetSteps = () => setStep(1);

  // validation
  const handleValidated = () => {
    if (selectedWallet === "") {
      toast.error("Please select wallet");
      return false;
    }
    if (recipientUID === "") {
      toast.error("Please enter a recipientUID");
      return false;
    }

    const amount = Number(requestAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    const wallet = wallets.find((w) => w.id === Number(selectedWallet));

    if (wallet) {
      const min = parseFloat(wallet.payment_limit.min.replace(/,/g, ""));
      const max = parseFloat(wallet.payment_limit.max.replace(/,/g, ""));

      const siteCurrencyCode = paymentSettings?.site_currency_code || "USD";
      const siteCurrencyDecimals = paymentSettings?.site_currency_decimals || 2;

      const decimals = dynamicDecimals({
        currencyCode: wallet.code,
        siteCurrencyCode,
        siteCurrencyDecimals,
        isCrypto: wallet.is_crypto,
      });

      if (amount < min || amount > max) {
        toast.error(
          `Amount must be between ${min.toFixed(decimals)} and ${max.toFixed(
            decimals
          )} ${wallet.code}`
        );
        return false;
      }
    }

    return true;
  };

  // get selected wallet object
  const selectedWalletObj = wallets.find(
    (w) => String(w.id) === selectedWallet
  );

  return (
    <>
      {/* Step 1 - Form */}
      {step === 1 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <div className="grid grid-cols-12 gap-5">
            {/* Wallet */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Wallet
              </label>
              <select
                className="border rounded w-full py-2 px-3 text-gray-700"
                value={selectedWallet}
                onChange={(e) => setSelectedWallet(e.target.value)}
              >
                <option value="">Select Wallet</option>
                {wallets.map((wallet) => (
                  <option key={wallet.id} value={wallet.id}>
                    {wallet.full_name_balance}
                  </option>
                ))}
              </select>
            </div>

            {/* Recipient UID */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Recipient UID
              </label>
              <input
                className="border rounded w-full py-2 px-3 text-gray-700"
                type="text"
                value={recipientUID}
                onChange={(e) => setRecipientUID(e.target.value)}
              />
            </div>

            {/* Amount */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Amount
              </label>
              <input
                className="border rounded w-full py-2 px-3 text-gray-700"
                type="number"
                value={requestAmount}
                onChange={(e) => setRequestAmount(e.target.value)}
              />
              {selectedWallet && (
                <p className="text-sm text-red-600 mt-1">
                  {
                    wallets.find((w) => w.id === Number(selectedWallet))
                      ?.payment_limit_text
                  }
                </p>
              )}
            </div>

            {/* Note */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Note (Optional)
              </label>
              <textarea
                className="w-full h-24 p-3 border rounded"
                placeholder="Enter your note..."
                value={selectNote}
                onChange={(e) => setSelectNote(e.target.value)}
              ></textarea>
            </div>

            {/* Next button */}
            <div className="col-span-12 flex justify-end">
              <button
                onClick={nextStep}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 w-full px-4 rounded"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2 - Confirmation */}
      {step === 2 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <h3 className="text-2xl font-semibold mb-5">Confirm Request</h3>
          <ul className="flex flex-col gap-3 text-gray-700 mb-6">
            <li className="flex justify-between">
              <strong>Wallet:</strong> {selectedWalletObj?.full_name_balance}
            </li>
            <li className="flex justify-between">
              <strong>Recipient UID:</strong> {recipientUID}
            </li>
            <li className="flex justify-between">
              <strong>Amount:</strong> {requestAmount} {selectedWalletObj?.code}
            </li>
            <li className="flex justify-between">
              <strong>Note:</strong> {selectNote || "N/A"}
            </li>
          </ul>

          <div className="flex gap-3">
            <button
              onClick={prevStep}
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded w-full"
            >
              Back
            </button>
            <button
              onClick={fetchRequestMoney}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded w-full"
            >
              Confirm & Proceed
            </button>
          </div>
        </div>
      )}
      {/* Step 3 - Status */}
      {step === 3 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
          <h3 className="text-2xl font-semibold mb-4">Request Sent 🎉</h3>
          <p className="text-gray-700 mb-6">
            Your request for{" "}
            <strong>
              {requestAmount} {selectedWalletObj?.code}
            </strong>{" "}
            has been sent to <strong>{recipientUID}</strong>.
          </p>
          <button
            onClick={resetSteps}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          >
            Make Another Request
          </button>
        </div>
      )}
    </>
  );
}

export default RequestMoney;
