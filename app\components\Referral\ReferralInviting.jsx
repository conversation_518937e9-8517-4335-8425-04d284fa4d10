"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import {
  Copy,
  Facebook,
  Linkedin,
  Twitter,
  Check,
  Users,
  DollarSign,
} from "lucide-react";
import Link from "next/link";

const ReferralInviting = () => {
  // fetch state
  const [referralInfo, setReferralInfo] = useState([]);
  const [directReferrals, setDirectReferrals] = useState([]);
  const [loading, setLoading] = useState(true);

  // others state
  const router = useRouter();

  const [copied, setCopied] = useState(false);
  const referralLink = "https://moneychain.tdevs.co/register?invite=fHKJap";

  const handleCopyLink = () => {
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // fetch referral tree
  const fetchReferralTree = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/referral/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setReferralInfo(res.data.data);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  // fetch Direct referrals
  const DirectReferrals = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/referral/direct`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setDirectReferrals(res.data.data || []);
    } catch (err) {
      console.log("API Error", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    } finally {
      setLoading(false);
    }
  };

  console.log("directReferrals", directReferrals);

  useEffect(() => {
    fetchReferralTree();
    DirectReferrals();
  }, []);

  return (
    <>
      <div className="min-h-screen rounded-xl bg-gray-900 text-white p-4 md:p-6">
        <div className="w-full">
          {/* Header */}
          <h1 className="text-2xl md:text-3xl font-bold mb-8">Referral</h1>

          {/* Main Referral Card */}
          <div className="bg-gray-800 rounded-2xl p-6 md:p-8 mb-8 relative overflow-hidden">
            {loading ? (
              <p className="text-gray-400 text-center text-sm">Loading...</p>
            ) : directReferrals.length > 0 ? (
              <div className="relative">
                {/* Earn Money Section */}
                <div className="mb-8">
                  <h2 className="text-3xl md:text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    Earn {referralInfo?.amount} after inviting one member
                  </h2>
                </div>

                {/* Referral Link */}
                <div className="mb-6">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1 bg-gray-700 rounded-lg px-4 py-3 font-mono text-sm text-gray-300 break-all">
                      {referralInfo?.link}
                    </div>
                    <button
                      onClick={handleCopyLink}
                      className="flex items-center justify-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 px-6 py-3 rounded-lg font-medium transition-all duration-200 whitespace-nowrap z-10"
                    >
                      {copied ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                      {copied ? "Copied!" : "Copy Link"}
                    </button>
                  </div>
                  <p className="text-sm text-gray-400 mt-2">
                    {referralInfo?.joined_text}
                  </p>
                </div>

                {/* Share URL Section */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Share URL:</h3>
                  <div className="flex gap-3">
                    <button className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-lg flex items-center justify-center transition-colors">
                      <Facebook className="w-5 h-5" />
                    </button>
                    <button className="w-10 h-10 bg-blue-500 hover:bg-blue-600 rounded-lg flex items-center justify-center transition-colors">
                      <Linkedin className="w-5 h-5" />
                    </button>
                    <button className="w-10 h-10 bg-gray-600 hover:bg-gray-700 rounded-lg flex items-center justify-center transition-colors">
                      <Twitter className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-400 text-sm">No data found.</p>
            )}

            <div className="absolute top-4 right-4 opacity-20">
              <div className="w-32 h-32 rounded-full border-2 border-purple-400">
                <div className="w-full h-full rounded-full border border-purple-400 flex items-center justify-center relative z-">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                    <DollarSign className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            </div>

            {/* Reward Conditions */}
            {referralInfo?.is_shown_referral_rules && (
              <div className="space-y-2">
                {referralInfo.rules?.map((ruleItem, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        ruleItem.icon === "tick" ? "bg-green-500" : "bg-red-500"
                      }`}
                    ></div>
                    <p className="text-sm text-gray-300">{ruleItem.rule}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Referred Friends Section */}
          <div className="bg-gray-800 rounded-2xl p-6 md:p-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <h2 className="text-2xl font-bold mb-4 sm:mb-0">
                Referred Friends
              </h2>
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-sm text-gray-400">Referral Profit</p>
                  <p className="text-xl font-bold text-green-400">$249.74</p>
                </div>
                <Link
                  href="/dashboard/referral/tree"
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 px-6 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap"
                >
                  Referral Tree
                </Link>
              </div>
            </div>

            {/* Friends List */}
            <div className="space-y-3">
              {loading ? (
                <p className="text-gray-400 text-center text-sm">Loading...</p>
              ) : directReferrals.length > 0 ? (
                directReferrals.map((friend, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 bg-gray-700 rounded-lg hover:bg-gray-650 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <img
                        src={friend.avatar}
                        alt={friend.username}
                        className="w-10 h-10 rounded-full object-cover border border-purple-400"
                      />
                      <div>
                        <p className="font-medium">{friend.username}</p>
                        <p className="text-sm text-gray-400">
                          {new Date(friend.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          friend.status
                            ? "bg-green-500/20 text-green-400"
                            : "bg-red-500/20 text-red-400"
                        }`}
                      >
                        {friend.status ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-400 text-sm">No referrals found.</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReferralInviting;
