import { useRouter } from 'next/router';
import { PAYMENT_ROUTES } from '../utils/routes';

export const useActiveRoute = () => {
  const router = useRouter();
  
  const isActiveRoute = (route) => {
    // For payment routes, check if current route is any payment-related route
    if (route === '/dashboard/make-payment') {
      return PAYMENT_ROUTES.includes(router.pathname);
    }
    
    return router.pathname === route;
  };
  
  const isPaymentRoute = () => {
    return PAYMENT_ROUTES.includes(router.pathname);
  };
  
  return {
    currentRoute: router.pathname,
    isActiveRoute,
    isPaymentRoute
  };
};