"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const Security = () => {
  const [qrCode, setQrCode] = useState(null);
  const [pin, setPin] = useState("");
  const [twoFAEnabled, setTwoFAEnabled] = useState(false);
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(true);

  const fetchTwoFAStatus = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/get`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: { google2fa_secret: 1 },
        }
      );

      if (res.data?.data?.two_fa === true) {
        setTwoFAEnabled(true);
      } else {
        setTwoFAEnabled(false);
      }
    } catch (err) {
      console.error(err.response?.data || err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTwoFAStatus();
  }, []);

  const handleGenerateQrCode = async () => {
    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/settings/2fa/generate`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setQrCode(res.data.data.qr_code);
      toast.success("QR Code generated successfully!");
    } catch (e) {
      console.log(e.response?.data);
    }
  };

  const handleEnable2FA = async () => {
    if (!pin) return toast.error("Please enter the PIN.");
    if (pin.length !== 6) return toast.error("The pin must be 6 digits.");

    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/settings/2fa/enable`,
        { one_time_password: pin },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success("2FA enabled successfully!");
      setTwoFAEnabled(true);
      setQrCode(null);
      setPin("");
    } catch (e) {
      toast.error(e.response?.data?.message || "Something went wrong.");
    }
  };

  const handleDisable2FA = async () => {
    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/settings/2fa/disable`,
        { one_time_password: password },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success("2FA disabled successfully!");
      setTwoFAEnabled(false);
    } catch (e) {
      toast.error(e.response?.data?.message || "Something went wrong.");
    }
  };

  if (loading) {
    return <p className="text-gray-600 p-4">Loading security settings...</p>;
  }

  return (
    <div className="bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
        {twoFAEnabled && (
          <div>
            <h3 className="text-green-600 font-medium text-lg">
              2FA is enabled
            </h3>
            <div className="mb-8">
              <input
                type="password"
                placeholder="Enter password"
                className="w-full max-w-md px-4 py-3 border border-gray-300 rounded-lg"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <button
              onClick={handleDisable2FA}
              className="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-6 rounded-lg flex items-center gap-2"
            >
              Disable 2FA
            </button>
          </div>
        )}

        {!twoFAEnabled && qrCode && (
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              2FA Authentication
            </h2>
            <p className="text-green-600 font-medium text-lg mb-8">
              Scan the QR code with Google Authenticator App to enable 2FA
            </p>
            <div
              className="inline-block bg-white p-4 border-2 border-gray-300 rounded-lg mb-8"
              dangerouslySetInnerHTML={{ __html: qrCode }}
            />
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter the PIN <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter 6-digit PIN"
                className="w-full max-w-md px-4 py-3 border border-gray-300 rounded-lg"
                value={pin}
                onChange={(e) => setPin(e.target.value)}
              />
            </div>
            <button
              type="button"
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg"
              onClick={handleEnable2FA}
            >
              Enable 2FA
            </button>
          </div>
        )}

        {!twoFAEnabled && !qrCode && (
          <div>
            <p className="text-gray-800 font-medium text-xl mb-8">
              2FA Authentication
            </p>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg"
              onClick={handleGenerateQrCode}
            >
              Generate QR Code
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Security;
