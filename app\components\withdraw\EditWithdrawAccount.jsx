"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const EditWithdrawAccount = ({ invoiceId }) => {
  const [withdrawAllAccountData, setWithdrawAllAccountData] = useState([]);
  const [formData, setFormData] = useState({});
  const [filePreviews, setFilePreviews] = useState({});
  const [methodName, setMethodName] = useState("");
  const router = useRouter();

  // Fetch withdraw account data
  const withdrawAllAccountDataGet = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setWithdrawAllAccountData(res.data.data);
    } catch (e) {
      console.log(e.response?.data || e.message);
    }
  };

  const selectedAccount = withdrawAllAccountData?.accounts?.find(
    (a) => a.id === Number(invoiceId)
  );

  // console.log("selectedAccount:", selectedAccount);

  // Initialize form values when account loads
  useEffect(() => {
    if (selectedAccount?.method?.fields) {
      const initialData = {};
      const initialFiles = {};

      selectedAccount.method.fields.forEach((field) => {
        initialData[field.name] = field.value || "";
        if (field.type === "file" && field.value) {
          initialFiles[field.name] = field.value;
        }
      });

      setFormData(initialData);
      setFilePreviews(initialFiles);
    }
    // Set method name when account loads
    if (selectedAccount?.method_name) {
      setMethodName(selectedAccount?.method_name);
    }
  }, [selectedAccount]);

  useEffect(() => {
    withdrawAllAccountDataGet();
  }, []);

  // Handle input change
  const handleChange = (e, field) => {
    if (field.type === "file") {
      const file = e.target.files[0];
      if (file) {
        setFormData((prev) => ({ ...prev, [field.name]: file }));
        setFilePreviews((prev) => ({
          ...prev,
          [field.name]: URL.createObjectURL(file),
        }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [field.name]: e.target.value }));
    }
  };

  // Handle method name change
  const handleMethodNameChange = (e) => {
    setMethodName(e.target.value);
  };

  // Handle submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    const data = new FormData();
    data.append("method_name", methodName);
    data.append("_method", "put");

    // Append credentials
    Object.entries(formData).forEach(([key, value]) => {
      if (value instanceof File) {
        data.append(`credentials[${key}]`, value);
      } else {
        data.append(`credentials[${key}]`, value);
      }
    });

    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts/${invoiceId}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );
      // console.log("Update success:", res.data);
      toast.success("Withdraw account updated successfully!");
      await withdrawAllAccountDataGet();
      router.push("/dashboard/withdraw/withdraw-account");
    } catch (err) {
      if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again.");
      }
    }
  };

  return (
    <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
      <form onSubmit={handleSubmit}>
        <h2 className="text-2xl font-bold mb-6">Edit Withdraw Account</h2>
        <div className="mb-4">
          <label className="block font-medium mb-1">Method Name</label>
          <input
            type="text"
            value={methodName}
            onChange={handleMethodNameChange}
            className="border rounded-md w-full p-2"
          />
        </div>
        {selectedAccount?.method?.fields?.map((field) => (
          <div key={field.name} className="mb-4">
            <label className="block font-medium mb-1">{field.name}</label>

            {field.type === "textarea" ? (
              <textarea
                name={field.name}
                value={formData[field.name] || ""}
                onChange={(e) => handleChange(e, field)}
                className="border rounded-md w-full p-2"
              />
            ) : field.type === "file" ? (
              <div>
                {filePreviews[field.name] && (
                  <img
                    src={filePreviews[field.name]}
                    alt={field.name}
                    className="mb-2 h-24 rounded object-cover"
                  />
                )}
                <input
                  type="file"
                  name={field.name}
                  onChange={(e) => handleChange(e, field)}
                  className="border rounded-md w-full p-2"
                />
              </div>
            ) : (
              <input
                type={field.type}
                name={field.name}
                value={formData[field.name] || ""}
                onChange={(e) => handleChange(e, field)}
                className="border rounded-md w-full p-2"
              />
            )}
          </div>
        ))}

        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded"
        >
          Update Account
        </button>
      </form>
    </div>
  );
};

export default EditWithdrawAccount;
