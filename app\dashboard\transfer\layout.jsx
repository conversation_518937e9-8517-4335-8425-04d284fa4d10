"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

const AddMoneyLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };
  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/transfer/money"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/transfer/money")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Transfer Money
        </Link>
        <Link
          href="/dashboard/transfer/history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/transfer/history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Transfer History
        </Link>
        <Link
          href="/dashboard/transfer/received"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/transfer/received")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Received History
        </Link>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default AddMoneyLayout;
