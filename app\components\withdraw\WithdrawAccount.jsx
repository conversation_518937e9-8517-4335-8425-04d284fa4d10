"use client";
import axios from "axios";
import Cookies from "js-cookie";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const WithdrawAccount = () => {
  const [withdrawAllAccountData, setWithdrawAllAccountData] = useState([]);
  const [loading, setLoading] = useState(false);
  // for pagination and filter
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState();
  const [perPage, setPerPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);

  const router = useRouter();
  console.log("withdrawAccountData:", withdrawAllAccountData);

  // Fetch withdraw account data
  const withdrawAllAccountDataGet = async () => {
    setLoading(true);
    try {
      const params = searchQuery
        ? { keyword: searchQuery }
        : {
            page: currentPage,
            ...(perPage && { per_page: perPage }),
          };
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
          params,
        }
      );
      setWithdrawAllAccountData(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setLoading(false);
    }
  };

  // handle search
  const handleSearch = () => {
    setCurrentPage(1);
    withdrawAllAccountDataGet();
  };

  // handle delete
  const handleDelete = async (id) => {
    try {
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts/${id}`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      toast.success("Withdraw account deleted successfully!");
      withdrawAllAccountDataGet();
    } catch (e) {
      console.log(e.response.data);
    }
  };

  useEffect(() => {
    withdrawAllAccountDataGet();
  }, [currentPage]);

  return (
    <div className="bg-white rounded-lg p-6">
      <div>
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4 bg-gray-100 rounded-lg p-3">
            <div>
              <input
                type="text"
                placeholder="Method Name"
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-700 text-sm">Per Page</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={perPage}
                onChange={(e) => setPerPage(e.target.value)}
              >
                <option value={1}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
              </select>
            </div>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
              onClick={handleSearch}
            >
              <span className="text-sm">Search</span>
            </button>
            <Link
              href="/dashboard/withdraw/withdraw-account/add"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
            >
              <span className="text-sm">Add New withdraw account</span>
            </Link>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    SL NO
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Account
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Action
                  </th>
                </tr>
              </thead>
              {loading ? (
                <tbody>
                  <tr>
                    <td colSpan={3} className="py-4 px-6 text-center">
                      Loading...
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="divide-y divide-gray-200">
                  {withdrawAllAccountData?.accounts?.map((item, index) => (
                    <tr
                      className="hover:bg-gray-50 transition-colors"
                      key={index}
                    >
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {index + 1}
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.method_name}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-700 flex gap-2">
                        <Link
                          href={`/dashboard/withdraw/withdraw-account/${item.id}/edit`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
                        >
                          <span className="text-sm">Edit</span>
                        </Link>
                        <button
                          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
                          onClick={() => handleDelete(item.id)}
                        >
                          <span className="text-sm">Delete</span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WithdrawAccount;
