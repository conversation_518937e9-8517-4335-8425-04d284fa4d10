"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

const WithdrawLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };
  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/withdraw/withdraw-money"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/withdraw/withdraw-money")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Withdraw Money
        </Link>
        <Link
          href="/dashboard/withdraw/withdraw-account"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/withdraw/withdraw-account")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Withdraw Account
        </Link>
        <Link
          href="/dashboard/withdraw/withdraw-history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/withdraw/withdraw-history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Withdraw History
        </Link>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default WithdrawLayout;
