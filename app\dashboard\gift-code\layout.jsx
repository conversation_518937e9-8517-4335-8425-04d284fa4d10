"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

const GiftCodeLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };
  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/gift-code/gift-redeem"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/gift-code/gift-redeem")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Gift Redeem
        </Link>
        <Link
          href="/dashboard/gift-code/create-gift"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/gift-code/create-gift")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Create Gift
        </Link>
        <Link
          href="/dashboard/gift-code/my-gift-history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/gift-code/my-gift-history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          My Gift History
        </Link>
        <Link
          href="/dashboard/gift-code/my-redeem-history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/gift-code/my-redeem-history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          My Redeem History
        </Link>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default GiftCodeLayout;
