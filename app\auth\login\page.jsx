"use client";

import axios from "axios";
import Cookies from "js-cookie";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-toastify";

const LoginPage = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);

  // Error states
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");

  const router = useRouter();

  const handleLogin = async (e) => {
    e.preventDefault();

    // Reset errors
    setEmailError("");
    setPasswordError("");

    if (!email.trim()) {
      setEmailError("Please enter a valid email address.");
      return;
    }
    if (!password.trim()) {
      setPasswordError("Please enter your password.");
      return;
    }

    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/login`,
        { email, password },
        { headers: { "Content-Type": "application/json" } }
      );

      const token = res.data.data.token;

      // Save token with js-cookie
      Cookies.set("token", token, {
        expires: rememberMe ? 7 : 1, // 7 days if "Remember Me" checked, else 1 day
        path: "/",
        secure: true,
        sameSite: "Lax",
      });

      toast.success("Login successful!");

      await checkTwoFa();
    } catch (err) {
      if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        console.error("Login error:", err.response?.data?.message || err.message);
        toast.error("Login failed. Please try again.");
      }
    }
  };

  const checkTwoFa = async () => {
    try {
      const token = Cookies.get("token"); // get token from cookie

      if (!token) {
        toast.error("No token found. Please login again.");
        router.push("/auth/login");
        return;
      }

      const settingsRes = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-settings?key=fa_verification`,
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      const siteTwoFaSetting = settingsRes.data.data;

      const userRes = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/get`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const userTwoFaSetting = userRes.data.data.two_fa;

      if (siteTwoFaSetting === "1" && userTwoFaSetting === true) {
        router.push("/auth/login/two-fa-verify");
      } else {
        router.push("/dashboard");
      }
    } catch (err) {
      console.error("Error in checkTwoFa:", err);
      toast.error("Something went wrong while checking 2FA.");
    }
  };

  // Check if user is already logged in
  // useEffect(() => {
  //   const token = localStorage.getItem("token");

  // //   if (token) {
  // //     router.replace("/dashboard");
  // //   }
  // // }, []);

  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <div className="w-full max-w-md bg-white shadow-lg rounded-lg p-8">
        <h1 className="text-3xl font-bold mb-5">Login</h1>
        <form onSubmit={handleLogin}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email
            </label>
            <input
              className={`appearance-none border rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline ${
                emailError ? "border-red-500" : "border-gray-300"
              }`}
              id="email"
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            {emailError && (
              <p className="text-red-500 text-sm mt-1">{emailError}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password
            </label>
            <input
              className={`appearance-none border rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline ${
                passwordError ? "border-red-500" : "border-gray-300"
              }`}
              id="password"
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
            {passwordError && (
              <p className="text-red-500 text-sm mt-1">{passwordError}</p>
            )}
          </div>

          <div className="mb-4 flex justify-between items-center">
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-5 w-5 text-blue-600"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <span className="ml-2 text-gray-700">Remember Me</span>
            </label>
            <Link href="/auth/login/forgot-password" className="text-blue-600">
              Forgot Password?
            </Link>
          </div>

          <div className="flex justify-center">
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
              type="submit"
            >
              Login
            </button>
          </div>

          <div className="mt-4 text-center">
            <p className="text-gray-700">
              Don't have an account?{" "}
              <Link href="/auth/register" className="text-blue-600">
                Register
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
