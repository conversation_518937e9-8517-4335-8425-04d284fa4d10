/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config) => {
    config.cache = false; // disable webpack caching
    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "qxpay.tdevs.co",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "moneychain.tdevs.co",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.gravatar.com",
        pathname: "/**",
      },
    ],
  },
};
export default nextConfig;
