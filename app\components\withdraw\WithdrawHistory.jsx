"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";

const WithdrawHistory = () => {
  const [withdrawHistory, setWithdrawHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  // for pagination and filter
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState();
  const [statusFilter, setStatusFilter] = useState("All");
  const [perPage, setPerPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);

  const withdrawHistoryData = async () => {
    setLoading(true);
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/transactions`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            type: "withdraw",
            ...(searchQuery && { txn: searchQuery }),
            page: currentPage,
            per_page: perPage,
            ...(statusFilter !== "All" && { status: statusFilter }),
          },
        }
      );
      setWithdrawHistory(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    withdrawHistoryData();
  };

  useEffect(() => {
    withdrawHistoryData();
  }, [currentPage, perPage, statusFilter]);

  console.log("withdrawHistory:", withdrawHistory);
  return (
    <div className="bg-white rounded-lg p-6">
      <div>
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4 bg-gray-100 rounded-lg p-3">
            <div>
              <input
                type="text"
                placeholder="Code"
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-700 text-sm">Status</span>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
              >
                <option value="All">All</option>
                <option value="Success">Success</option>
                <option value="Pending">Pending</option>
                <option value="Failed">Failed</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-700 text-sm">Per Page</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={perPage}
                onChange={(e) => setPerPage(e.target.value)}
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
              </select>
            </div>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
              onClick={handleSearch}
            >
              <span className="text-sm">Search</span>
            </button>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Description
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Transaction ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Wallet
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Type
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Amount
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Charge
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Method
                  </th>
                </tr>
              </thead>
              {loading ? (
                <tbody>
                  <tr>
                    <td colSpan={8} className="py-4 px-6 text-center">
                      Loading...
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="divide-y divide-gray-200">
                  {withdrawHistory?.transactions?.map((item, index) => (
                    <tr
                      className="hover:bg-gray-50 transition-colors"
                      key={index}
                    >
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <div className="w-3 h-3 bg-white rounded-full"></div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-800">
                              {item.description}
                            </div>
                            <div className="text-xs text-gray-500">
                              {item.created_at}
                            </div>
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.tnx}
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.wallet_type}
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.type}
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.amount}
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.charge}
                      </td>

                      {item.status === "Success" ? (
                        <td className="px-6 py-4 text-sm">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Success
                          </span>
                        </td>
                      ) : item.status === "Pending" ? (
                        <td className="px-6 py-4 text-sm">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Pending
                          </span>
                        </td>
                      ) : (
                        <td className="px-6 py-4 text-sm">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Failed
                          </span>
                        </td>
                      )}
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {item.method}
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WithdrawHistory;
