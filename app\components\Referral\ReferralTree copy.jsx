"use client";

import React, { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

const ReferralTreeNode = ({ node }) => {
  if (!node) return null;

  return (
    <li className={node.is_me ? "" : "child"}>
      <div className={node.is_me ? "referral-tree-item-parent" : "referral-tree-item tree-children"}>
        <div className="referral-tree-card-inner">
          <div className={`referral-tree-card ${node.is_me ? "tree-parent" : ""}`}>
            <div className="thumb">
              <img src={node.avatar} alt={node.name} />
            </div>
            <div className="content">
              <h5 className="title">{node.is_me ? `Me (${node.name})` : node.name}</h5>
              {!node.is_me && <p className="info">Deposit 50, Invest $0, ROI Profit $0</p>}
            </div>
          </div>
        </div>

        {node.children && node.children.length > 0 && (
          <ul>
            {node.children.map((child) => (
              <ReferralTreeNode key={child.id} node={child} />
            ))}
          </ul>
        )}
      </div>
    </li>
  );
};

const ReferralTree = () => {
  const router = useRouter();
  const [treeData, setTreeData] = useState(null);

  useEffect(() => {
    const fetchTreeData = async () => {
      try {
        const res = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/referral/tree`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        });
        setTreeData(res.data.data);
      } catch (err) {
        console.log("API Error", err);
        if (err.response?.status === 401) {
          Cookies.remove("token");
          router.push("/auth/login");
        }
      }
    };
    fetchTreeData();
  }, [router]);

  return (
    <div className="min-h-screen rounded-xl bg-gray-900 text-white pt-3 p-6">
      <h3 className="text-2xl md:text-3xl font-bold mb-8">Referral Tree</h3>
      {treeData ? (
        <div className="referral-tree-main overflow-x-auto">
          <div className="td-referral-tree">
            <ul>
              <ReferralTreeNode node={treeData} />
            </ul>
          </div>
        </div>
      ) : (
        <p className="text-center">Loading tree...</p>
      )}
    </div>
  );
};

export default ReferralTree;
