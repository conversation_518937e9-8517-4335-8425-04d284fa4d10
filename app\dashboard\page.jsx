"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import ActivityLineChart from "../components/charts/ActivityLineChart";
import { useRouter } from "next/navigation";
import RecentTransactions from "../components/RecentTransactions/RecentTransactions";
import WalletsCards from "../components/WalletsCards/WalletsCards";
import UserStatistics from "./UserStatistics/UserStatistics";
const Dashboard = () => {
  const [dashboard, setDashboard] = useState(null);
  const [loading, setLoading] = useState(true);

  // for Referral copy url
  const [copied, setCopied] = useState(false);

  const handleReferralCopy = () => {
    if (!dashboard?.referral?.link) return;
    navigator.clipboard.writeText(dashboard.referral.link).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const router = useRouter();

  const fetchDashboard = async () => {
    setLoading(true);
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/dashboard`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      // console.log("Dashboard Data:", res.data.data.statistics);
      setDashboard(res.data.data);
    } catch (err) {
      console.error("API Error:", err);
      if (err.response.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboard();
  }, []);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 text-purple-500 animate-spin mx-auto mb-4" />
          <p className="text-black text-lg">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-12 gap-y-6">
        <div className="col-span-12">
          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-6">
              {/* Profile Card  */}
              <div className="relative bg-gray-800 rounded-2xl p-8 overflow-hidden border border-gray-700 h-full">
                {/* Floating 3D Elements  */}
                <div className="absolute inset-0 pointer-events-none">
                  {/* Purple Torus  */}
                  <div className="absolute top-12 right-16 w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full animate-float opacity-80 shadow-2xl"></div>

                  {/* Colorful Pill  */}
                  <div className="absolute top-8 right-32 w-16 h-8 bg-gradient-to-r from-pink-400 via-purple-500 to-cyan-400 rounded-full animate-float-delayed opacity-90 shadow-xl"></div>

                  {/* Blue Cylinder  */}
                  <div className="absolute top-32 right-20 w-6 h-16 bg-gradient-to-b from-blue-400 to-blue-600 rounded-full animate-float opacity-85 shadow-lg"></div>

                  {/* Purple Pyramid  */}
                  <div className="absolute top-24 right-48 w-0 h-0 animate-float-delayed opacity-80">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-purple-800 transform rotate-45 shadow-xl"></div>
                  </div>

                  {/* White Sphere  */}
                  <div className="absolute bottom-20 right-12 w-14 h-14 bg-gradient-to-br from-white to-gray-200 rounded-full animate-float opacity-90 shadow-2xl"></div>

                  {/* Teal Semi-circle  */}
                  <div className="absolute bottom-16 right-40 w-16 h-8 bg-gradient-to-r from-teal-400 to-emerald-500 rounded-t-full animate-float-delayed opacity-85 shadow-lg"></div>
                </div>

                {/* Content  */}
                <div className="relative z-10">
                  <div className="mb-6">
                    <p className="text-purple-400 text-xl font-medium mb-4">
                      Hello,{dashboard?.info?.time_wise_wish}
                    </p>
                    <h1 className="text-white text-5xl font-bold mb-6">
                      {dashboard?.user?.full_name}
                    </h1>
                    <div className="inline-flex items-center bg-gradient-to-r from-orange-400 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      UID:{dashboard?.user?.account_number}
                    </div>
                  </div>

                  {/* Login Info  */}
                  <div className="text-gray-400 text-sm mt-8"></div>
                </div>
              </div>
            </div>
            <div className="col-span-6">
              {/* Referral Card  */}
              <div className="bg-gray-800 rounded-2xl p-8 flex-1 border border-gray-700">
                <div className="mb-6">
                  <h2 className="text-white text-2xl font-bold mb-2">
                    Referral Link
                  </h2>
                  <p className="text-gray-400 text-sm">
                    Share this referral link with your friends and earn money
                  </p>
                </div>

                {/* Referral Link Input  */}
                <div className="bg-gray-700 rounded-lg p-4 mb-4 flex items-center justify-between">
                  <input
                    type="text"
                    value={dashboard?.referral?.link || ""}
                    readOnly
                    className="bg-transparent text-gray-300 text-sm flex-1 outline-none"
                  />
                  <button
                    onClick={handleReferralCopy}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 flex items-center gap-2 ml-4"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      ></path>
                    </svg>
                    {copied ? "Copied" : "Copy Link"}
                  </button>
                </div>

                {/* Status  */}
                <div className="text-sm">
                  <span className="text-orange-400 font-semibold">
                    {dashboard?.referral?.count} users
                  </span>
                  <span className="text-gray-400">
                    have joined from your referral link.
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-12">
          <UserStatistics
            statisticsData={dashboard?.statistics}
          ></UserStatistics>
        </div>
        <div className="col-span-12">
          <WalletsCards
            walletsCardsData={dashboard?.userWallets}
          ></WalletsCards>
        </div>
        <div className="col-span-12">
          <div className="bg-gray-800 rounded-2xl p-8 relative">
            <ActivityLineChart wallets={dashboard?.userWallets} />
          </div>
        </div>
        <div className="col-span-12">
          <RecentTransactions
            transactionsData={dashboard?.transactions}
          ></RecentTransactions>
        </div>
      </div>
    </>
  );
};

export default Dashboard;
