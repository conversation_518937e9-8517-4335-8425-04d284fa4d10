"use client";
import { useSettings } from "@/context/SettingsContext";
import { dynamicDecimals, getSettingValue } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";

const PaymentHistory = () => {
  // fetch state
  const [historyData, setHistoryData] = useState();

  // stored state
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState();

  //stored state
  const [perPage, setPerPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);

  // sate for site currency
  const { settings } = useSettings();
  const siteCurrency = getSettingValue(settings, "site_currency");
  const siteCurrencyDecimals = getSettingValue(
    settings,
    "site_currency_decimals"
  );

  // Fetch payment history
  const fetchPaymentHistory = async () => {
    try {
      const params = searchQuery
        ? { tnx_id: searchQuery }
        : {
            page: currentPage,
            ...(perPage && { per_page: perPage }),
          };

      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/payment/history`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params,
        }
      );

      setHistoryData(res.data.data);
      setLastPage(res.data.data.pagination.last_page);
    } catch (err) {
      console.error("API Error", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      } else if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again");
      }
    }
  };

  useEffect(() => {
    fetchPaymentHistory();
  }, [currentPage]);

  console.log(perPage);

  console.log(historyData);
  // Get status badge class based on status
  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case "success":
        return "bg-green-900 text-green-300";
      case "pending":
        return "bg-yellow-900 text-yellow-300";
      case "failed":
        return "bg-red-900 text-red-300";
      default:
        return "bg-gray-700 text-gray-300";
    }
  };

  const handleHistorySearch = () => {
    setCurrentPage(1);
    fetchPaymentHistory();
  };

  return (
    <>
      <h3 className="text-3xl font-bold mb-5">Payment History</h3>
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="bg-card-bg rounded-lg border border-gray-700 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center w-1/3">
            <div className="flex-1 w-full sm:w-auto">
              <input
                type="text"
                placeholder="Transaction ID"
                className="w-full bg-transparent border border-gray-700 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-primary focus:ring-1 focus:ring-purple-primary transition-colors"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="w-full sm:w-auto min-w-[120px]">
              <div className="relative">
                <select
                  className="w-full bg-gray-700 border text-gray-50 border-gray-700 rounded-md px-3 py-2 focus:outline-none focus:border-purple-primary focus:ring-1 focus:ring-purple-primary transition-colors appearance-none cursor-pointer"
                  value={perPage}
                  onChange={(e) => setPerPage(e.target.value)}
                >
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="200">200</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </div>
            </div>

            <button
              onClick={handleHistorySearch}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-[100]"
            >
              Search
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Description
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Transaction ID
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Merchant
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Wallet
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Amount
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Charge
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Status
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Method
                </th>
              </tr>
            </thead>
            <tbody>
              {historyData?.transactions.map((item, index) => (
                <tr
                  key={item.id || index}
                  className="border-b border-gray-700 hover:bg-gray-750 transition-colors"
                >
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="text-white font-medium">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg text-yellow-400">💰</span>
                          <div>
                            <div className="text-white font-medium">
                              {item.description}
                            </div>
                            <div className="text-gray-400 text-sm">
                              {item.created_at}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-gray-300 font-mono text-sm">
                    {item.tnx}
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    {item.merchant?.name || "Unknown Merchant"}
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    <span>{item.wallet_name}</span>
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    {Number(item.amount).toFixed(
                      dynamicDecimals({
                        currencyCode: item.currency,
                        siteCurrencyCode: siteCurrency,
                        siteCurrencyDecimals: siteCurrencyDecimals,
                        isCrypto: item?.is_crypto,
                      })
                    )}{" "}
                    {item.currency}
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    <span>
                      {Number(item.charge).toFixed(
                        dynamicDecimals({
                          currencyCode: item.currency,
                          siteCurrencyCode: siteCurrency,
                          siteCurrencyDecimals: siteCurrencyDecimals,
                          isCrypto: item?.is_crypto,
                        })
                      )}{" "}
                      {item.currency}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    <span
                      className={`px-2 py-1 rounded-md text-xs ${getStatusClass(
                        item.status
                      )}`}
                    >
                      {item.status}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-gray-300">{item.method}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="p-4">
          {/* Pagination */}
          {historyData?.pagination && (
            <div className="flex items-center">
              <nav className="flex items-center space-x-1">
                {/* Prev Button */}
                <button
                  onClick={async () => {
                    setCurrentPage((prev) => Math.max(prev - 1, 1));
                    await fetchPaymentHistory();
                  }}
                  disabled={currentPage === 1}
                  className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md 
          ${
            currentPage === 1
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-40"
          }`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 19l-7-7 7-7"
                    ></path>
                  </svg>
                </button>

                {/* Page Numbers */}
                {[...Array(lastPage)].map((_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-8 h-8 flex items-center justify-center border border-gray-700 rounded-md 
              ${
                currentPage === pageNum
                  ? "bg-blue-500 text-white"
                  : "text-gray-300"
              }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, lastPage))
                  }
                  disabled={currentPage === lastPage}
                  className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md 
          ${
            currentPage === perPage
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-40"
          }`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </button>
              </nav>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PaymentHistory;
