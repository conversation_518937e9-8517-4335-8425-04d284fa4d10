"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

const SettingsLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };
  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/settings/account-settings"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/settings/account-settings")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Account Settings
        </Link>
        <Link
          href="/dashboard/settings/change-password"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/settings/change-password")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Change Password
        </Link>
        <Link
          href="/dashboard/settings/security"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/settings/security")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Security
        </Link>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default SettingsLayout;
