import React from "react";

const UserStatistics = ({ statisticsData }) => {
  console.log(statisticsData);

  // Convert object to array of { key, value }
  const statsArray = Object.entries(statisticsData).map(([key, value]) => ({
    id: key,
    name: key,
    value: value,
  }));

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
      {statsArray?.map((item) => (
        <div
          key={item.id}
          className="bg-gradient-to-br from-slate-800 to-slate-700 rounded-2xl p-6 border border-white/10 hover:border-white/20 transform hover:-translate-y-1 transition-all duration-300 shadow-xl hover:shadow-2xl"
        >
          <div className="flex justify-between items-start mb-4">
            <h3 className="text-slate-400 text-sm font-medium uppercase tracking-wide">
              {item.name.replace(/_/g, " ")}
            </h3>
            <div className="w-10 h-10 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-xl flex items-center justify-center border border-white/10">
              <span className="text-xl">💰</span>
            </div>
          </div>
          <div className="text-white text-3xl font-bold">{item.value}</div>
        </div>
      ))}
    </div>
  );
};

export default UserStatistics;
