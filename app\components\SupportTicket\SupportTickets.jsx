"use client";
import axios from "axios";
import Cookies from "js-cookie";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";

function SupportTickets() {
  const [tickets, setTickets] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);
  const [loading, setLoading] = useState(true);

  const fetchTickets = async () => {
    setLoading(true);
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/ticket`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            page: currentPage,
            ...(perPage && { per_page: perPage }),
          },
        }
      );

      const { tickets, pagination } = res.data.data;
      setTickets(tickets || []);
      setLastPage(pagination.last_page || 1);

      if (tickets.length === 0) {
        toast.info("No tickets available");
      }
    } catch (err) {
      console.error("API Error", err);
      if (err.response?.status === 401) {
        toast.info("No tickets available");
      } else if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, [currentPage]);

  // Get status badge class
  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case "open":
        return "bg-green-900 text-green-300";
      case "pending":
        return "bg-yellow-900 text-yellow-300";
      case "closed":
        return "bg-red-900 text-red-300";
      default:
        return "bg-gray-700 text-gray-300";
    }
  };

  return (
    <>
      <div className="inline-flex items-center justify-between mb-8 w-full">
        <h3 className="text-2xl md:text-3xl font-bold">Support Tickets</h3>
        <Link
          href="/dashboard/support/create"
          className="bg-gradient-to-r from-purple-500 text-white to-pink-500 hover:from-purple-600 hover:to-pink-600 px-6 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap"
        >
          Create Ticket
        </Link>
      </div>
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Ticket Title
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Status
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Priority
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={6} className="text-center text-gray-400 py-6">
                    Loading...
                  </td>
                </tr>
              ) : tickets.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center text-gray-400 py-6">
                    No exchange history
                  </td>
                </tr>
              ) : (
                tickets.map((item) => (
                  <tr
                    key={item.id}
                    className="border-b border-gray-700 hover:bg-gray-750 transition-colors"
                  >
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-3">
                        <img
                          src={item.user.avatar}
                          alt={item.user.name}
                          className="w-8 h-8 rounded-full"
                        />
                        <div>
                          <div className="text-white font-medium">
                            {item.title}
                          </div>
                          <div className="text-gray-400 text-sm">
                            {new Date(item.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span
                        className={`px-2 py-1 rounded-md capitalize text-sm font-medium ${getStatusClass(
                          item.status
                        )}`}
                      >
                        {item.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <span className="px-3 py-1 rounded-full text-sm capitalize font-medium bg-green-500/20 text-green-400">
                        {item.priority}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <Link
                        // href={`/support/ticket/${item.uuid}`}
                        href={`/dashboard/support/chat/${item.uuid}/show`}
                        className="px-3 py-1 rounded-full text-sm capitalize font-medium bg-blue-500/20 text-blue-400"
                      >
                        View
                      </Link>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {lastPage > 1 && (
          <div className="p-4 flex">
            <nav className="flex flex-wrap space-x-1">
              {/* Prev */}
              <button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md ${
                  currentPage === 1
                    ? "text-gray-500 cursor-not-allowed"
                    : "text-gray-400 hover:bg-gray-700"
                }`}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>

              {/* Pages */}
              {[...Array(lastPage)].map((_, i) => {
                const pageNum = i + 1;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-8 h-8 flex items-center justify-center border border-gray-700 rounded-md ${
                      currentPage === pageNum
                        ? "bg-blue-500 text-white"
                        : "text-gray-300 hover:bg-gray-700"
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              {/* Next */}
              <button
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, lastPage))
                }
                disabled={currentPage === lastPage}
                className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md ${
                  currentPage === lastPage
                    ? "text-gray-500 cursor-not-allowed"
                    : "text-gray-400 hover:bg-gray-700"
                }`}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </nav>
          </div>
        )}
      </div>
    </>
  );
}

export default SupportTickets;
