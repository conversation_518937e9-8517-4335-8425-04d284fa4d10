"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const RecentTransactions = () => {
  const [transactions, setTransactions] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState();

  // console.log(searchQuery)
  // console.log(statusFilter);

  const router = useRouter();

  const fetchResTransactionsData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/transactions`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            page: currentPage,
            ...(statusFilter !== "All" && { status: statusFilter }),
            ...(searchQuery !== "" && { txn: searchQuery }),
          },
        }
      );
      setTransactions(res.data.data.transactions);
      const lastPage = res.data.data.meta.last_page;
      setPerPage(lastPage);
    } catch (err) {
      console.error("API Error:", err);

      if (err.response && err.response.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  useEffect(() => {
    fetchResTransactionsData();
  }, [currentPage]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchResTransactionsData();
  };

  return (
    <>
      <div className="card">
        <h1 className="text-2xl font-bold mb-6">Transactions</h1>
        <div className="bg-gray-900 text-white min-h-screen p-6 rounded-lg">
          <div className="bg-card-bg rounded-lg border border-gray-700 p-4 mb-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center w-1/3">
              <div className="flex-1 w-full sm:w-auto">
                <input
                  type="text"
                  placeholder="Transaction ID"
                  className="w-full bg-transparent border border-gray-700 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-primary focus:ring-1 focus:ring-purple-primary transition-colors"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="w-full sm:w-auto min-w-[120px]">
                <div className="relative">
                  <select
                    className="w-full bg-gray-700 border text-gray-50 border-gray-700 rounded-md px-3 py-2 focus:outline-none focus:border-purple-primary focus:ring-1 focus:ring-purple-primary transition-colors appearance-none cursor-pointer"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="">All</option>
                    <option value="pending">Pending</option>
                    <option value="success">Success</option>
                    <option value="failed">Failed</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                  </div>
                </div>
              </div>

              <button
                onClick={handleSearch}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-[100]"
              >
                Search
              </button>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Description
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Transaction ID
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Type
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Amount
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Charge
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Status
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Method
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((transactions, index) => (
                    <tr
                      key={transactions.tnx}
                      className="border-b border-gray-700 hover:bg-gray-750 transition-colors"
                    >
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <span className={`text-lg text-yellow-400`}>💰</span>
                          <div>
                            <div className="text-white font-medium">
                              {transactions.description}
                            </div>
                            <div className="text-gray-400 text-sm">
                              {transactions.created_at}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-gray-300 font-mono text-sm">
                        {transactions.tnx}
                      </td>
                      <td className="py-4 px-4 text-gray-300">
                        {transactions.type}
                      </td>
                      <td className="py-4 px-4">
                        <span
                          className={
                            transactions.is_plus
                              ? "text-green-400"
                              : "text-red-400"
                          }
                        >
                          {transactions.is_plus ? "+" : "-"}
                          {transactions.amount}
                          <span className="ml-1 text-xs">
                            {transactions.is_plus ? "↑" : "↓"}
                          </span>
                        </span>
                      </td>
                      <td className="py-4 px-4 text-red-400 font-medium">
                        {transactions.charge}
                      </td>
                      <td className="py-4 px-4">
                        <span
                          className={
                            transactions.status === "Failed"
                              ? "text-red-400"
                              : "text-green-400"
                          }
                        >
                          {transactions.status}
                        </span>
                      </td>
                      <td className="py-4 px-4 text-gray-300">
                        {transactions.method}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="bg-card-bg rounded-lg p-4">
            {transactions.length > 0 && searchQuery === "" && (
              <div className="flex items-center">
                <nav className="flex items-center flex-wrap space-x-1 space-y-1">
                  {/* Prev Button */}
                  <button
                    onClick={async () => {
                      setCurrentPage((prev) => Math.max(prev - 1, 1));
                      await fetchResTransactionsData();
                    }}
                    disabled={currentPage === 1}
                    className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md 
          ${
            currentPage === 1
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-40"
          }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 19l-7-7 7-7"
                      ></path>
                    </svg>
                  </button>

                  {/* Page Numbers */}
                  {[...Array(perPage)].map((_, i) => {
                    const pageNum = i + 1;
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`w-8 h-8 flex items-center justify-center border border-gray-700 rounded-md 
              ${
                currentPage === pageNum
                  ? "bg-blue-500 text-white"
                  : "text-gray-300"
              }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, perPage))
                    }
                    disabled={currentPage === perPage}
                    className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md 
          ${
            currentPage === perPage
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-40"
          }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5l7 7-7 7"
                      ></path>
                    </svg>
                  </button>
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default RecentTransactions;
