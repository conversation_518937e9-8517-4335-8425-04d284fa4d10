"use client";
import Link from "next/link";
import React, { useState } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

function SupportCreate() {
  // state
  const [formTitle, setFormTitle] = useState("");
  const [formDescription, setFormDescription] = useState("");
  const [attachments, setAttachments] = useState([{ id: 1, file: null }]);
  const [loading, setLoading] = useState(false);
  
    const router = useRouter();

  // utils
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // attachment handlers
  const addAttachment = () => {
    const newId = Math.max(...attachments.map((a) => a.id), 0) + 1;
    setAttachments([...attachments, { id: newId, file: null }]);
  };

  const handleFileChange = (fieldId, file) => {
    setAttachments((prev) =>
      prev.map((a) => (a.id === fieldId ? { ...a, file } : a))
    );
  };

  const removeAttachment = (fieldId) => {
    if (attachments.length > 1) {
      setAttachments((prev) => prev.filter((a) => a.id !== fieldId));
    } else {
      setAttachments([{ id: 1, file: null }]);
    }
  };

  // APi call
  const submitCreateTicket = async () => {
    try {
      setLoading(true);

      const formData = new FormData();
      formData.append("title", formTitle);
      formData.append("message", formDescription);

      attachments.forEach((a) => {
        if (a.file) {
          formData.append("attachments[]", a.file);
        }
      });

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/ticket`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success(res.data?.message || "Ticket submitted successfully!");

      // const ticketCode = res.data?.ticket?.ticket_code;
      // if (ticketCode) {
      //   router.push("");
      // }

      // reset form
      setFormTitle("");
      setFormDescription("");
      setAttachments([{ id: 1, file: null }]);

      return true;
    } catch (err) {
      if (err.response?.status === 422) {
        toast.error(err.response?.data?.message || "Validation error");
      } else if (err.response?.data?.message) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  // form submit handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formTitle.trim() || !formDescription.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    await submitCreateTicket();
  };

  return (
    <>
      {/* Header */}
      <div className="inline-flex items-center justify-between mb-8 w-full">
        <h3 className="text-2xl md:text-3xl font-bold">Add New Ticket</h3>
        <Link
          href="/dashboard/support"
          className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all"
        >
          All Tickets
        </Link>
      </div>

      {/* Form */}
      <div className="bg-gray-900 min-h-screen p-6 rounded-lg">
        <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formTitle}
                onChange={(e) => setFormTitle(e.target.value)}
                className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:outline-none"
                placeholder="Enter ticket title"
                required
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                value={formDescription}
                onChange={(e) => setFormDescription(e.target.value)}
                rows="6"
                className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:outline-none resize-vertical"
                placeholder="Describe your issue in detail"
                required
              />
            </div>

            {/* Attachments */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-300">
                  Attachments
                </label>
                <button
                  type="button"
                  onClick={addAttachment}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
                >
                  Add Attach
                </button>
              </div>

              <div className="space-y-4">
                {attachments.map((field, index) => (
                  <div key={field.id}>
                    <div className="flex items-center gap-3 mb-1 justify-between">
                      <span className="text-sm text-gray-400">
                        Attachment {index + 1}
                      </span>
                      {attachments.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeAttachment(field.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          ✕
                        </button>
                      )}
                    </div>

                    {!field.file ? (
                      <input
                        type="file"
                        onChange={(e) =>
                          handleFileChange(field.id, e.target.files[0])
                        }
                        className="border-2 border-dashed w-full text-white rounded-lg p-8 text-center bg-gray-900/50 transition-colors duration-200 cursor-pointer border-gray-600 hover:border-purple-500"
                      />
                    ) : (
                      <div className="flex items-center justify-between p-4 bg-gray-900 rounded-lg border border-gray-600">
                        <div>
                          <p className="text-white">{field.file.name}</p>
                          <p className="text-gray-400 text-sm">
                            {formatFileSize(field.file.size)}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <button
                            type="button"
                            onClick={() =>
                              document
                                .getElementById(`file-${field.id}`)
                                .click()
                            }
                            className="text-purple-400 hover:text-purple-300 text-sm"
                          >
                            Change
                          </button>
                          <button
                            type="button"
                            onClick={() => removeAttachment(field.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            ✕
                          </button>
                          <input
                            id={`file-${field.id}`}
                            type="file"
                            className="hidden"
                            onChange={(e) =>
                              handleFileChange(field.id, e.target.files[0])
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Submit */}
            <div className="flex justify-end pt-6">
              <button
                type="submit"
                disabled={loading}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-medium transition-all transform disabled:opacity-50"
              >
                {loading ? "Submitting..." : "Add Ticket"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}

export default SupportCreate;
