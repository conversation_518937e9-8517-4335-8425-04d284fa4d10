"use client";

import { dynamicDecimals } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import React, { useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";

const ExchangeWallet = () => {
  // fetched data
  const [fromWallets, setFromWallets] = useState([]);
  const [configData, setConfigData] = useState(null);

  // form state
  const [fromWallet, setFromWallet] = useState(""); // currency_id or "0" for Main
  const [toWallet, setToWallet] = useState(""); // currency_id or "0" for Main
  const [amount, setAmount] = useState("");
  const [transferResponse, setTransferResponse] = useState(null);

  // derived amounts
  const [calculatedCharge, setCalculatedCharge] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);

  // ui
  const router = useRouter();
  const [step, setStep] = useState(1);

  // Helpers
  const findByCurrencySelection = (sel) => {
    if (!sel) return undefined;
    if (sel === "0") return fromWallets.find((w) => w.is_default); // Main
    return fromWallets.find((w) => Number(w.currency_id) === Number(sel));
  };

  const selectedFrom = useMemo(
    () => findByCurrencySelection(fromWallet),
    [fromWallet, fromWallets]
  );
  const selectedTo = useMemo(
    () => findByCurrencySelection(toWallet),
    [toWallet, fromWallets]
  );

  // Compute USD→currency for any wallet
  const usdTo = (wallet) => {
    if (!wallet) return 1;
    if (wallet.is_default || wallet.code === "USD") return 1; // Main wallet
    const r = parseFloat(wallet.conversion_rate ?? "1");
    return Number.isFinite(r) && r > 0 ? r : 1;
  };

  // from → to exchange rate using USD as bridge
  const computeExchangeRate = () => {
    if (!selectedFrom || !selectedTo) return 1;
    const rate = usdTo(selectedTo) / usdTo(selectedFrom);
    return Number.isFinite(rate) && rate > 0 ? rate : 1;
  };

  const exchangeRate = useMemo(computeExchangeRate, [selectedFrom, selectedTo]);
  const exchangeAmount = useMemo(() => {
    const base = parseFloat(amount || "0");
    if (!Number.isFinite(base)) return "0";
    const out = base * exchangeRate;
    // format to target wallet decimals (roughly 8 for crypto, else site/default)
    const siteCurrencyCode = configData?.site_currency_code || "USD";
    const siteCurrencyDecimals = configData?.site_currency_decimals || 2;
    const targetDecimals = dynamicDecimals({
      currencyCode: selectedTo?.code || siteCurrencyCode,
      siteCurrencyCode,
      siteCurrencyDecimals,
      isCrypto: !!selectedTo?.is_crypto,
    });
    return out.toFixed(Math.min(Math.max(targetDecimals, 2), 8));
  }, [amount, exchangeRate, selectedTo, configData]);

  // Fetching Wallets Data
  const fetchWalletData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: { exchange: 1 },
        }
      );

      // Normalize: ensure Main wallet selectable via "0"
      const wallets = (res.data?.data?.wallets ?? []).map((w) => ({
        ...w,
        // keep currency_id as-is; main wallet has no currency_id; we'll use "0" when rendering value
      }));

      setFromWallets(wallets);
    } catch (err) {
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      } else {
        toast.error("Failed to load wallets.");
      }
    }
  };

  // Fetching Exchange Data
  const fetchExchangeData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/exchange/config`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setConfigData(res.data?.data ?? null);
    } catch (err) {
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      } else {
        toast.error("Failed to load exchange config.");
      }
    }
  };

  // Submit API Exchange
  const submitExchange = async () => {
    console.log("Check amount", amount);
    console.log("Check from_wallet", fromWallet);
    console.log("Check to_wallet", toWallet);
    try {
      const from = selectedFrom;
      const to = selectedTo;

      const requestBody = {
        amount: amount,
        from_wallet: from?.is_default ? "default" : from?.currency_id,
        to_wallet: to?.is_default ? "default" : to?.currency_id,
      };

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/exchange`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success(res.data?.message || "Exchange successful");
      setTransferResponse(res.data);
      return true;
    } catch (err) {
      if (err.response?.status === 422) {
        toast.error(err.response?.data?.message || "Validation error");
      } else if (err.response?.data?.message) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again.");
      }
      return false;
    }
  };

  useEffect(() => {
    fetchWalletData();
    fetchExchangeData();
  }, []);

  // Charges
  useEffect(() => {
    const baseAmount = parseFloat(amount);
    if (
      !Number.isFinite(baseAmount) ||
      baseAmount <= 0 ||
      !configData?.settings
    ) {
      setCalculatedCharge(0);
      setTotalAmount(0);
      return;
    }

    const { charge_type, charge } = configData.settings || {};
    const type = (charge_type || "").toLowerCase();
    const chargeNum = parseFloat(charge) || 0;

    let chargeValue = 0;
    if (type === "percentage") chargeValue = (baseAmount * chargeNum) / 100;
    else if (type === "fixed") chargeValue = chargeNum;

    setCalculatedCharge(chargeValue);
    setTotalAmount(baseAmount + chargeValue);
  }, [amount, configData]);

  // Validation
  const handleValidated = () => {
    if (!fromWallet) {
      toast.error("Please select From Wallet");
      return false;
    }
    if (!toWallet) {
      toast.error("Please select To Wallet");
      return false;
    }
    if (fromWallet === toWallet) {
      toast.error("From and To Wallet cannot be same");
      return false;
    }

    const amt = Number(amount);
    if (!Number.isFinite(amt) || amt <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    // limits - use selectedFrom (currency_id / default)
    const wallet = selectedFrom;

    if (wallet && wallet.exchange_limit) {
      const minWallet =
        parseFloat(
          (wallet.exchange_limit.min || "0").toString().replace(/,/g, "")
        ) || 0;
      const maxWallet =
        parseFloat(
          (wallet.exchange_limit.max || "0").toString().replace(/,/g, "")
        ) || Infinity;

      const siteCurrencyCode = configData?.site_currency_code || "USD";
      const siteCurrencyDecimals = configData?.site_currency_decimals || 2;
      const decimals = dynamicDecimals({
        currencyCode: wallet.code,
        siteCurrencyCode,
        siteCurrencyDecimals,
        isCrypto: wallet.is_crypto,
      });

      if (amt < minWallet || amt > maxWallet) {
        toast.error(
          `Amount must be between ${minWallet.toFixed(
            decimals
          )} and ${maxWallet.toFixed(decimals)} ${wallet.code}`
        );
        return false;
      }
    } else {
      // fallback config limit
      const minConfig = parseFloat(configData?.settings?.minimum_amount) || 0;
      const maxConfig =
        parseFloat(configData?.settings?.maximum_amount) || Infinity;
      if (amt < minConfig || amt > maxConfig) {
        toast.error(`Amount must be between ${minConfig} and ${maxConfig}`);
        return false;
      }
    }

    return true;
  };

  // Step nav
  const nextStep = async () => {
    if (!handleValidated()) return;
    setStep((s) => Math.min(s + 1, 3));
  };
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));
  const resetSteps = () => setStep(1);
  return (
    <>
      {/* Step 1 - Form */}
      {step === 1 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <div className="grid grid-cols-12 gap-5">
            {/* From Wallet */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                From Wallet
              </label>
              <select
                className="border rounded w-full py-2 px-3"
                value={fromWallet}
                onChange={(e) => {
                  setFromWallet(e.target.value);
                  // reset toWallet if same as from
                  if (e.target.value === toWallet) setToWallet("");
                }}
              >
                <option value="">Select Wallet</option>
                {fromWallets.map((w, idx) => (
                  <option
                    key={w.currency_id ?? `main-${idx}`}
                    value={w.currency_id ?? "0"} // Main -> "0"
                  >
                    {w.full_name_balance}
                  </option>
                ))}
              </select>
            </div>

            {/* To Wallet */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                To Wallet
              </label>
              <select
                className="border rounded w-full py-2 px-3"
                value={toWallet}
                onChange={(e) => setToWallet(e.target.value)}
              >
                <option value="">Select Wallet</option>
                {fromWallets.map((w, idx) => {
                  const value = w.currency_id ?? "0";
                  return (
                    <option key={w.currency_id ?? `main-${idx}`} value={value}>
                      {w.full_name_balance}
                    </option>
                  );
                })}
              </select>
            </div>

            {/* Amount */}
            <div className="col-span-12">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Amount
              </label>
              <input
                className="border rounded w-full py-2 px-3"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder={`Enter amount in ${selectedFrom?.code || ""}`}
              />

              {amount && (
                <p className="text-sm text-gray-600 mt-1">
                  Charge: {calculatedCharge} {selectedFrom?.code || ""} | Total:{" "}
                  {totalAmount} {selectedFrom?.code || ""}
                </p>
              )}

              {fromWallet && (
                <p className="text-sm text-red-600 mt-1">
                  {selectedFrom?.exchange_limit_text}
                </p>
              )}
            </div>

            {/* Next */}
            <div className="col-span-12 flex justify-end">
              <button
                onClick={nextStep}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 w-full px-4 rounded"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2 - Confirmation */}
      {step === 2 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
          <h3 className="text-2xl border-b border-gray-300 font-semibold mb-5 pb-3">
            Confirm Exchange
          </h3>

          <ul className="flex gap-4 flex-col mb-6 text-gray-700">
            <li className="flex justify-between">
              <span className="fw-7">From Wallet</span>
              <span>{selectedFrom?.full_name}</span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">Amount</span>
              <span>
                {amount || 0} {selectedFrom?.code}
              </span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">Charge</span>
              <span>
                {calculatedCharge.toFixed(2)} {selectedFrom?.code}
              </span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">Charge type</span>
              <span>{configData?.settings?.charge_type || "N/A"}</span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">Total Amount</span>
              <span className="text-blue-600 font-semibold">
                {totalAmount.toFixed(2)} {selectedFrom?.code}
              </span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">To Wallet</span>
              <span>{selectedTo?.full_name}</span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">Exchange Rate</span>
              <span>
                1 {selectedFrom?.code} = {exchangeRate} {selectedTo?.code}
              </span>
            </li>
            <li className="flex justify-between">
              <span className="fw-7">Exchange Amount</span>
              <span>
                {exchangeAmount} {selectedTo?.code}
              </span>
            </li>
          </ul>

          <div className="flex gap-3">
            <button
              onClick={prevStep}
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded w-full"
            >
              Back
            </button>
            <button
              onClick={async () => {
                const ok = handleValidated();
                if (!ok) return;
                const success = await submitExchange();
                if (success) setStep(3);
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded w-full"
            >
              Confirm & Proceed
            </button>
          </div>
        </div>
      )}

      {/* Step 3 - Success */}
      {step === 3 && (
        <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
          <h3 className="text-3xl font-semibold text-green-600 mb-4">
            {transferResponse?.message}
          </h3>
          <div className="grid grid-cols-12 gap-6 mb-5">
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Transaction ID</p>
                <p>{transferResponse?.data.transaction?.tnx}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Description</p>
                <p>{transferResponse?.data.transaction?.description}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Charge</p>
                <p>{transferResponse?.data.transaction?.charge}</p>
              </div>
            </div>
            <div className="col-span-6">
              <div className="border py-6 rounded-lg">
                <p>Final Amount</p>
                <p>{transferResponse?.data.transaction?.final_amount}</p>
              </div>
            </div>
          </div>
          <button
            onClick={async () => {
              resetSteps();
              setTransferResponse(null);
              setFromWallet("");
              setToWallet("");
              setAmount("");
              setCalculatedCharge(0);
              setTotalAmount(0);

              // fetch wallets again to refresh balances
              await fetchWalletData();
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          >
            Make Another Exchange
          </button>
        </div>
      )}
    </>
  );
};

export default ExchangeWallet;
