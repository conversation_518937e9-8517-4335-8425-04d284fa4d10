"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const EditInvoice = ({ invoiceId }) => {
  const [items, setItems] = useState([
    { id: 0, name: "", quantity: "", price: "", subtotal: "" },
  ]);

  const [to, setTo] = useState("");
  const [email, setEmail] = useState("");
  const [address, setAddress] = useState("");
  const [currency, setCurrency] = useState("");
  const [issueDate, setIssueDate] = useState("");
  const [published, setPublished] = useState("");

  const [singleInvoiceData, setSingleInvoiceData] = useState({});

  const [wallets, setWallets] = useState([]);
  const [walletsLoading, setWalletsLoading] = useState(false);

  const router = useRouter();

  // get wallets data
  const walletsData = async () => {
    try {
      setWalletsLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setWallets(res.data.data.wallets);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setWalletsLoading(false);
    }
  };

  const fetchSingleInvoiceData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/invoices/${invoiceId}`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const invoiceData = res.data.data;
      setSingleInvoiceData(invoiceData);
      setTo(invoiceData?.to);
      setEmail(invoiceData?.email);
      setAddress(invoiceData?.address);
      setCurrency(invoiceData?.currency);
      setIssueDate(
        invoiceData?.issue_date
          ? invoiceData.issue_date.split("T")[0] // keeps only "2025-08-29"
          : ""
      );
      setPublished(invoiceData?.is_published ? "1" : "0");

      // Populate items state with fetched data
      if (invoiceData?.items && invoiceData.items.length > 0) {
        const formattedItems = invoiceData.items.map((item, index) => ({
          id: index,
          name: item.name,
          quantity: item.quantity.toString(),
          price: item.unit_price.toString(),
          subtotal: item.subtotal.toString(),
        }));
        setItems(formattedItems);
      }
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // Add new item with incremental ID
  const handleAddItem = () => {
    setItems((prev) => [
      ...prev,
      {
        id: prev.length > 0 ? prev[prev.length - 1].id + 1 : 0,
        name: "",
        quantity: "",
        price: "",
        subtotal: "",
      },
    ]);
  };

  const handleUpdateInvoice = async (e) => {
    e.preventDefault();

    try {
      const requestBody = {
        to: to,
        email: email,
        address: address,
        currency: currency,
        issue_date: issueDate ? new Date(issueDate).toISOString() : null,
        is_published: published,
        _method: "put",
        items: items.map((item) => ({
          name: item.name,
          quantity: item.quantity,
          unit_price: item.price,
        })),
      };
      console.log("requestBody:", requestBody);
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/invoices/${singleInvoiceData.id}`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );
      console.log(res.data);
      toast.success("Invoice update successfully!");
      router.push("/dashboard/create-invoice/history");
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // Remove item but keep at least one
  const handleRemoveItem = (id) => {
    if (items.length === 1) {
      toast.warning("At least one item must remain!");
      return;
    }
    setItems(items.filter((item) => item.id !== id));
  };

  // Update item fields
  const handleChange = (id, field, value) => {
    setItems((prev) =>
      prev.map((item) =>
        item.id === id
          ? {
              ...item,
              [field]: value,
              subtotal:
                field === "quantity" || field === "price"
                  ? (field === "quantity"
                      ? Number(value)
                      : Number(item.quantity)) *
                    (field === "price" ? Number(value) : Number(item.price))
                  : item.subtotal,
            }
          : item
      )
    );
  };

  useEffect(() => {
    walletsData();
  }, []);

  useEffect(() => {
    fetchSingleInvoiceData();
  }, [invoiceId]);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Invoice Information
          </h1>
        </div>
        <form onSubmit={handleUpdateInvoice}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Invoice To <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg  outline-none transition-colors bg-white text-gray-900"
                value={to}
                onChange={(e) => setTo(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg  outline-none transition-colors bg-white text-gray-900"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg  outline-none transition-colors bg-white text-gray-900"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Wallet <span className="text-red-500">*</span>
              </label>
              <select
                className="w-full px-4 py-3 border border-gray-300 rounded-lg  outline-none transition-colors bg-white text-gray-900"
                disabled={walletsLoading}
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
              >
                <option value="">
                  {walletsLoading ? "Loading wallets..." : "Select Wallet"}
                </option>
                {wallets.map((wallet) => (
                  <option value={wallet.code} key={wallet.id}>
                    {wallet.full_name_balance}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Issue Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none bg-white text-gray-900"
                value={issueDate} // always "YYYY-MM-DD"
                onChange={(e) => setIssueDate(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status <span className="text-red-500">*</span>
              </label>
              <select
                className="w-full px-4 py-3 border border-gray-300 rounded-lg  outline-none transition-colors bg-white text-gray-900 cursor-pointer"
                value={published}
                onChange={(e) => setPublished(e.target.value)}
              >
                <option value="0">Draft</option>
                <option value="1">Published</option>
              </select>
            </div>
          </div>

          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Invoice Items</h2>
            <button
              type="button"
              onClick={handleAddItem}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg flex items-center gap-2"
            >
              <span className="text-lg">+</span>
              Add Item
            </button>
          </div>

          {items.map((item) => (
            <div
              key={item.id}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6 items-center"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Item Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={item.name}
                  onChange={(e) =>
                    handleChange(item.id, "name", e.target.value)
                  }
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none bg-white text-gray-900"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  value={item.quantity}
                  onChange={(e) =>
                    handleChange(item.id, "quantity", e.target.value)
                  }
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none bg-white text-gray-900"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Unit Price <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  value={item.price}
                  onChange={(e) =>
                    handleChange(item.id, "price", e.target.value)
                  }
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none bg-white text-gray-900"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sub Total
                </label>
                <input
                  type="number"
                  value={item.subtotal}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none bg-gray-100 text-gray-900"
                />
              </div>

              <div className="mt-[25px]">
                <button
                  type="button"
                  onClick={() => handleRemoveItem(item.id)}
                  className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
          <div className="flex justify-start">
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              type="submit"
            >
              Update Invoice
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditInvoice;
