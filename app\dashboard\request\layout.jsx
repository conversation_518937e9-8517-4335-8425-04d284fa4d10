"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

const AddMoneyLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };
  return (
    <div>
      {/* Navigation Header */}
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <Link
          href="/dashboard/request/money"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/request/money")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Request Money
        </Link>
        <Link
          href="/dashboard/request/received"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/request/received")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Received Requests
        </Link>
        <Link
          href="/dashboard/request/history"
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              isActive("/dashboard/request/history")
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Request History
        </Link>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default AddMoneyLayout;
