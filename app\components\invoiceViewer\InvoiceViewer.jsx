"use client";
import { useSettings } from "@/context/SettingsContext";
import { dynamicDecimals, getSettingValue } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";

const InvoiceViewer = ({ invoiceId }) => {
  const [singleInvoiceData, setSingleInvoiceData] = useState({});
  // console.log("singleInvoiceData:", singleInvoiceData);
  const { settings } = useSettings();

  const fetchSingleInvoiceData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/invoices/${invoiceId}`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setSingleInvoiceData(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    }
  };

  useEffect(() => {
    fetchSingleInvoiceData();
  }, []);

  return (
    <div>
      <p>Invoice ID: {invoiceId}</p>
      <div className="min-h-screen bg-gray-50 text-gray-900 p-6">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-2xl p-8">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                Moneychain
              </span>
            </div>
            <div className="text-right text-gray-600">
              <div className="text-sm">Ref: #{singleInvoiceData.number}</div>
              <div className="text-sm">
                Issued:
                {new Date(singleInvoiceData.issue_date).toLocaleDateString(
                  "en-US",
                  {
                    day: "2-digit",
                    month: "short",
                    year: "numeric",
                  }
                )}
              </div>
            </div>
          </div>

          {/* Customer and Total Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                {singleInvoiceData.to}
              </h2>
              <div className="text-gray-600 mb-2">
                {singleInvoiceData.email}
              </div>
              <div className="text-gray-600 mb-4">
                {singleInvoiceData.address}
              </div>
              <div className="inline-block">
                {singleInvoiceData.is_paid ? (
                  <span className="bg-green-100 text-green-800 text-xs font-medium px-3 py-1 rounded-full">
                    Paid
                  </span>
                ) : (
                  <span className="bg-red-100 text-red-800 text-xs font-medium px-3 py-1 rounded-full">
                    Unpaid
                  </span>
                )}
              </div>
            </div>

            <div className="text-right">
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                Total Amount:
              </h3>
              <div className="text-2xl font-bold text-gray-900 mb-4">
                {singleInvoiceData.total_amount} {singleInvoiceData.currency}
              </div>
              <div className="text-gray-600 text-sm space-y-1">
                <div>
                  Amount: {singleInvoiceData.amount}{" "}
                  {singleInvoiceData.currency}
                </div>
                <div>
                  Charge: {singleInvoiceData.charge}{" "}
                  {singleInvoiceData.currency}
                </div>
              </div>
            </div>
          </div>

          {/* Invoice Items Table */}
          <div className="mb-8">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b-2 border-gray-200">
                    <th className="text-left py-4 px-0 text-gray-700 font-semibold">
                      Item Name
                    </th>
                    <th className="text-left py-4 px-4 text-gray-700 font-semibold">
                      Quantity
                    </th>
                    <th className="text-left py-4 px-4 text-gray-700 font-semibold">
                      Unit Price
                    </th>
                    <th className="text-right py-4 px-0 text-gray-700 font-semibold">
                      Subtotal
                    </th>
                  </tr>
                </thead>
                {singleInvoiceData?.items?.map((item) => (
                  <tbody className="divide-y divide-gray-200" key={item.id}>
                    <tr className="text-gray-900">
                      <td className="py-4 px-0">{item.name}</td>
                      <td className="py-4 px-4">{item.quantity}</td>
                      <td className="py-4 px-4">
                        {parseFloat(item.unit_price).toFixed(
                          dynamicDecimals({
                            currencyCode: singleInvoiceData.currency,
                            siteCurrencyCode: getSettingValue(
                              settings,
                              "site_currency"
                            ),
                            siteCurrencyDecimals: getSettingValue(
                              settings,
                              "site_currency_decimals"
                            ),
                            isCrypto: singleInvoiceData.is_crypto,
                          })
                        )}{" "}
                        {singleInvoiceData.currency}
                      </td>
                      <td className="py-4 px-0 text-right">
                        {parseFloat(item.subtotal).toFixed(
                          dynamicDecimals({
                            currencyCode: singleInvoiceData.currency,
                            siteCurrencyCode: getSettingValue(
                              settings,
                              "site_currency"
                            ),
                            siteCurrencyDecimals: getSettingValue(
                              settings,
                              "site_currency_decimals"
                            ),
                            isCrypto: singleInvoiceData.is_crypto,
                          })
                        )}{" "}
                        {singleInvoiceData.currency}
                      </td>
                    </tr>
                  </tbody>
                ))}
              </table>
            </div>

            {/* Summary Section */}
            <div className="mt-6 border-t-2 border-gray-200 pt-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center text-gray-600">
                  <span>Subtotal</span>
                  <span>
                    {singleInvoiceData.amount} {singleInvoiceData.currency}
                  </span>
                </div>
                <div className="flex justify-between items-center text-gray-600">
                  <span>Charge</span>
                  <span>
                    {singleInvoiceData.charge} {singleInvoiceData.currency}
                  </span>
                </div>
                <div className="flex justify-between items-center text-lg font-bold text-gray-900 border-t-2 border-gray-200 pt-2">
                  <span>Total Amount</span>
                  <span className="text-green-600">
                    {singleInvoiceData.total_amount}{" "}
                    {singleInvoiceData.currency}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Thank You Message */}
          <div className="text-gray-500 text-sm mb-8">
            Thanks for the purchase.
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
              Print Invoice
            </button>
            <a
              href={singleInvoiceData?.transaction?.payment_gateway_url}
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Pay Now
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceViewer;
