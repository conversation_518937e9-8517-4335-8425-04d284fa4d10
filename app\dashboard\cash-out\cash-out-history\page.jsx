"use client";
import { useSettings } from "@/context/SettingsContext";
import { dynamicDecimals, getSettingValue } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const page = () => {
  const [cashOutHistory, setCashOutHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  // console.log("cashOutHistory:", cashOutHistory);
  const { settings } = useSettings();
  const siteCurrency = getSettingValue(settings, "site_currency");
  const siteCurrencyDecimals = getSettingValue(
    settings,
    "site_currency_decimals"
  );

  // for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState();
  const [perPage, setPerPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);

  const fetchCashOutHistory = async () => {
    setLoading(true);
    try {
      const params = searchQuery
        ? { tnx_id: searchQuery }
        : {
            page: currentPage,
            ...(perPage && { per_page: perPage }),
          };
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/cashout/history`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params,
        }
      );
      setCashOutHistory(res.data.data);
    } catch (e) {
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
      }
      console.log(e.response.data);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchCashOutHistory();
  };

  useEffect(() => {
    fetchCashOutHistory();
  }, [currentPage]);
  return (
    <div className="bg-white rounded-lg p-6">
      <div>
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4 bg-gray-100 rounded-lg p-3">
            <div>
              <input
                type="text"
                placeholder="Code"
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-700 text-sm">Per Page</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={perPage}
                onChange={(e) => setPerPage(e.target.value)}
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
              </select>
            </div>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
              onClick={handleSearch}
            >
              <span className="text-sm">Search</span>
            </button>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Description
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Transaction ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Wallet
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Amount
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Charge
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Method
                  </th>
                </tr>
              </thead>
              {loading ? (
                <tbody>
                  <tr>
                    <td colSpan={7} className="py-4 px-6 text-center">
                      Loading...
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="divide-y divide-gray-200">
                  {cashOutHistory?.cashouts?.map((cashout) => (
                    <tr
                      key={cashout.id}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      {/* Description + Date */}
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <div className="w-3 h-3 bg-white rounded-full"></div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-800">
                              {cashout.description}
                            </div>
                            <div className="text-xs text-gray-500">
                              {cashout.created_at}
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Transaction ID */}
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {cashout.tnx}
                      </td>

                      {/* Wallet Name */}
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {cashout.wallet_name}
                      </td>

                      {/* Amount */}
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {Number(cashout.amount).toFixed(
                          dynamicDecimals({
                            currencyCode: cashout.currency,
                            siteCurrencyCode: siteCurrency,
                            siteCurrencyDecimals: siteCurrencyDecimals,
                            isCrypto: cashout?.is_crypto,
                          })
                        )}{" "}
                        {cashout.currency}
                      </td>

                      {/* Charge */}
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {Number(cashout.charge).toFixed(
                          dynamicDecimals({
                            currencyCode: cashout.currency,
                            siteCurrencyCode: siteCurrency,
                            siteCurrencyDecimals: siteCurrencyDecimals,
                            isCrypto: cashout?.is_crypto,
                          })
                        )}{" "}
                        {cashout.currency}
                      </td>

                      {/* Status */}
                      <td className="px-6 py-4 text-sm">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
            ${
              cashout.status === "Success"
                ? "bg-green-100 text-green-800"
                : cashout.status === "Pending"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
            }`}
                        >
                          {cashout.status}
                        </span>
                      </td>

                      {/* Agent Name (with account number) */}
                      <td className="px-6 py-4 text-sm text-gray-700 ">
                        {cashout.method}
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
