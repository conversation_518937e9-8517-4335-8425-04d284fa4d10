"use client";
import { useSettings } from "@/context/SettingsContext";
import { dynamicDecimals, getSettingValue } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const WithdrawMoney = () => {
  const [step, setStep] = useState(1);
  const [withdrawAccount, setWithdrawAccount] = useState([]);
  // console.log("withdrawAccount:", withdrawAccount);
  const [withdrawAccountLoading, setWithdrawAccountLoading] = useState(false);
  const [selectWithdrawAccount, setSelectWithdrawAccount] = useState("");
  const [selectAmount, setSelectAmount] = useState("");
  const [calculatedCharge, setCalculatedCharge] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [withdrawResponse, setWithdrawResponse] = useState(null);

  const { settings } = useSettings();
  const siteCurrency = getSettingValue(settings, "site_currency");
  const siteCurrencyDecimals = getSettingValue(
    settings,
    "site_currency_decimals"
  );

  const withdrawAccountData = async () => {
    setWithdrawAccountLoading(true);
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw-accounts`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setWithdrawAccount(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setWithdrawAccountLoading(false);
    }
  };

  // find selected account data
  const selectedAccount = withdrawAccount?.accounts?.find(
    (a) => a.id === Number(selectWithdrawAccount)
  );

  // console.log("selectedAccount:", selectedAccount.id);

  //validation
  const handleValidated = () => {
    if (selectWithdrawAccount === "") {
      toast.error("Please select withdraw account");
      return false;
    }

    const amount = Number(selectAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    // check min and max from selectedAccount
    if (selectedAccount) {
      const min = Number(selectedAccount.method.min_withdraw);
      const max = Number(selectedAccount.method.max_withdraw);

      // use helper
      const decimals = dynamicDecimals({
        currencyCode: selectedAccount.currency,
        siteCurrencyCode: siteCurrency,
        siteCurrencyDecimals: siteCurrencyDecimals,
        isCrypto: selectedAccount.method.is_crypto,
      });

      if (amount < min || amount > max) {
        toast.error(
          `Amount must be between ${min.toFixed(decimals)} ${
            selectedAccount.currency
          } to ${max.toFixed(decimals)} ${selectedAccount.currency}`
        );
        return false;
      }
    }

    return true;
  };

  const reviewCalculate = async () => {
    const baseAmount = parseFloat(selectAmount);
    let chargeValue = 0;
    if (selectedAccount?.method?.charge_type?.toLowerCase() === "percentage") {
      chargeValue =
        (baseAmount * parseFloat(selectedAccount.method.charge)) / 100;
      const total = baseAmount + chargeValue;
      setCalculatedCharge(chargeValue);
      setTotalAmount(total);
    } else if (
      selectedAccount?.method?.charge_type?.toLowerCase() === "fixed"
    ) {
      await convertFixedAmount(selectedAccount.method.charge, baseAmount);
    } else {
      toast.error("Something went wrong, please try again.");
    }
  };

  // if fixed amount
  const convertFixedAmount = async (charge, amount) => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/convert/${charge}/${selectedAccount?.code}/true`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const convertedCharge = Number(res.data.data.converted_amount);
      setCalculatedCharge(convertedCharge);
      const total = amount + convertedCharge;
      setTotalAmount(total);
    } catch (e) {
      console.error(e.response?.data || e.message);
    }
  };

  const handleWithdrawSubmit = async () => {
    if (!handleValidated()) return;
    try {
      const requestBody = {
        amount: selectAmount,
        withdraw_account_id: selectWithdrawAccount,
      };

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/withdraw`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      toast.success("Withdraw request sent successfully!");
      setWithdrawResponse(res.data.data);
      setStep(3);
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // Step navigation
  const nextStep = () => {
    if (!handleValidated()) return;
    reviewCalculate();
    setStep((prev) => Math.min(prev + 1, 3));
  };
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));
  const resetSteps = () => setStep(1);

  useEffect(() => {
    withdrawAccountData();
  }, []);

  return (
    <div>
      {/* Step Indicators */}
      <div className="max-w-lg mx-auto mb-4">
        <div className="flex justify-between">
          {[1, 2, 3].map((s) => (
            <div key={s} className={`step-${s}`}>
              <div className="flex flex-col items-center">
                <div
                  className={`step-number w-8 h-8 flex justify-center items-center rounded-full 
                    ${step === s ? "bg-blue-600 text-white" : "bg-gray-200"}`}
                >
                  {s}
                </div>
                <div className="step-name">
                  {s === 1 ? "Amount" : s === 2 ? "Review" : "Success"}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
        {step === 1 && (
          <div className="step-1-content">
            <div className="grid grid-cols-12 gap-5">
              {/* Wallet */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Withdraw Account
                </label>
                <select
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  disabled={withdrawAccountLoading}
                  value={selectWithdrawAccount}
                  onChange={(e) => setSelectWithdrawAccount(e.target.value)}
                >
                  <option value="">
                    {withdrawAccountLoading
                      ? "Loading Accounts..."
                      : "Select Account"}
                  </option>
                  {withdrawAccount?.accounts?.map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.method_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Amount */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Amount
                </label>
                <input
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  type="number"
                  placeholder="Amount"
                  value={selectAmount}
                  onChange={(e) => setSelectAmount(e.target.value)}
                />
                {selectWithdrawAccount && (
                  <p className="text-sm text-red-600 mt-1">
                    Minimum: {selectedAccount.method.min_withdraw}{" "}
                    {selectedAccount.currency} and Maximum:{" "}
                    {selectedAccount.method.max_withdraw}{" "}
                    {selectedAccount.currency}
                  </p>
                )}
              </div>

              {/* Next Button */}
              <div className="col-span-12 flex justify-end">
                <button
                  onClick={nextStep}
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="step-2-content">
            <p className="mb-4 text-xl font-medium border-b border-b-gray-500 pb-2">
              Review Details
            </p>
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Amount</h3>
                <p>
                  {selectAmount} {selectedAccount?.currency}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Charge</h3>
                <p>
                  {calculatedCharge} {selectedAccount?.currency}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Total</h3>
                <p>
                  {totalAmount} {selectedAccount?.currency}
                </p>
              </div>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded"
              >
                Back
              </button>
              <button
                onClick={handleWithdrawSubmit}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Withdraw Now
              </button>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="step-3-content text-center">
            <div className="mb-4 border-b pb-4">
              <p className="mb-4 text-xl font-semibold">
                Your Withdraw Process is Pending!
              </p>
              <p>
                {Number(selectAmount).toFixed(
                  dynamicDecimals({
                    currencyCode: selectedAccount?.currency,
                    siteCurrencyCode: siteCurrency,
                    siteCurrencyDecimals: siteCurrencyDecimals,
                    isCrypto: selectedAccount?.method?.is_crypto || false,
                  })
                )}{" "}
                {selectedAccount?.currency} Withdraw Pending
              </p>
            </div>
            <div className="mb-4">
              <p className="font-bold">Amount</p>
              <p>
                {Number(selectAmount).toFixed(
                  dynamicDecimals({
                    currencyCode: selectedAccount?.currency,
                    siteCurrencyCode: siteCurrency,
                    siteCurrencyDecimals: siteCurrencyDecimals,
                    isCrypto: selectedAccount?.method?.is_crypto || false,
                  })
                )}{" "}
                {selectedAccount?.currency}
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Transaction ID</p>
                <p>{withdrawResponse?.transaction?.tnx}</p>
              </div>
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Charge</p>
                <p>
                  {Number(calculatedCharge).toFixed(
                    dynamicDecimals({
                      currencyCode: selectedAccount?.currency,
                      siteCurrencyCode: siteCurrency,
                      siteCurrencyDecimals: siteCurrencyDecimals,
                      isCrypto: selectedAccount?.method?.is_crypto || false,
                    })
                  )}{" "}
                  {selectedAccount?.currency}
                </p>
              </div>
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Transaction Type</p>
                <p>{withdrawResponse?.transaction?.type}</p>
              </div>
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Final Amount</p>
                <p>
                  {Number(totalAmount).toFixed(
                    dynamicDecimals({
                      currencyCode: selectedAccount?.currency,
                      siteCurrencyCode: siteCurrency,
                      siteCurrencyDecimals: siteCurrencyDecimals,
                      isCrypto: selectedAccount?.method?.is_crypto || false,
                    })
                  )}{" "}
                  {selectedAccount?.currency}
                </p>
              </div>
            </div>
            <div className="flex justify-end">
              <button
                onClick={resetSteps}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Withdraw Again
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WithdrawMoney;
