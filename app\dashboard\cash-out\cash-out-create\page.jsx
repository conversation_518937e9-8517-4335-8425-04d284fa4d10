"use client";

import { dynamicDecimals } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const page = () => {
  const [step, setStep] = useState(1);
  const [wallets, setWallets] = useState([]);
  const [walletsLoading, setWalletsLoading] = useState(false);
  const [selectWallet, setSelectWallet] = useState("");
  const [selectAmount, setSelectAmount] = useState("");
  const [selectAgentAID, setSelectAgentAID] = useState("");
  const [paymentSettings, setPaymentSettings] = useState([]);
  const [cashOutConfig, setCashOutConfig] = useState([]);
  const [calculatedCharge, setCalculatedCharge] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const router = useRouter();

  // get wallets data
  const walletsData = async () => {
    try {
      setWalletsLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
          params: {
            cashout: 1,
          },
        }
      );
      setWallets(res.data.data.wallets);
      // console.log(res.data.data.wallets);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setWalletsLoading(false);
    }
  };

  // Fetch payment setting
  const fetchPaymentSettings = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/payment/settings`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setPaymentSettings(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // validation
  const handleValidated = () => {
    if (selectAgentAID === "") {
      toast.error("Please enter a agent AID");
      return false;
    }

    if (selectWallet === "") {
      toast.error("Please select wallet");
      return false;
    }

    const amount = Number(selectAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    const wallet = wallets.find((w) => w.id === Number(selectWallet));
    if (wallet) {
      // clean numbers (remove commas if any)
      const min = parseFloat(wallet.cashout_limit.min.replace(/,/g, ""));
      const max = parseFloat(wallet.cashout_limit.max.replace(/,/g, ""));

      // FIX: siteCurrencyCode/siteCurrencyDecimals from paymentSettings
      const siteCurrencyCode = paymentSettings?.site_currency_code || "USD"; // fallback
      const siteCurrencyDecimals = paymentSettings?.site_currency_decimals || 2;

      // use helper
      const decimals = dynamicDecimals({
        currencyCode: wallet.code,
        siteCurrencyCode,
        siteCurrencyDecimals,
        isCrypto: wallet.is_crypto,
      });

      if (amount < min || amount > max) {
        toast.error(
          `Amount must be between ${min.toFixed(decimals)} and ${max.toFixed(
            decimals
          )} ${wallet.code}`
        );
        return false;
      }
    }

    return true;
  };

  // find selected wallet data
  const selectedWallet = wallets.find((w) => w.id === Number(selectWallet));
  // console.log("selectedWallet", selectedWallet);

  // Step navigation
  const nextStep = () => {
    if (!handleValidated()) return;
    reviewCalculate();
    setStep((prev) => Math.min(prev + 1, 2));
  };
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));
  const resetSteps = () => setStep(1);

  const CashOutConfigData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/cashout/config`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setCashOutConfig(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // charge amount calculate
  const reviewCalculate = async () => {
    if (!selectAmount || !cashOutConfig?.settings) return;

    const amount = parseFloat(selectAmount);
    if (isNaN(amount) || amount <= 0) return;

    const chargeType = cashOutConfig.settings.charge_type?.toLowerCase();
    const chargeValue = parseFloat(cashOutConfig.settings.charge);

    let finalCharge = 0;
    if (chargeType === "percentage") {
      finalCharge = (amount * chargeValue) / 100;
      setCalculatedCharge(finalCharge);
      const total = amount + finalCharge;
      setTotalAmount(total);
    } else if (chargeType === "fixed") {
      await convertFixedAmount(chargeValue, amount);
    }
  };

  // if fixed amount
  const convertFixedAmount = async (charge, amount) => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/convert/${charge}/${selectedWallet?.code}/true`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const convertedCharge = Number(res.data.data.converted_amount);
      setCalculatedCharge(convertedCharge);
      const total = amount + convertedCharge;
      setTotalAmount(total);
    } catch (e) {
      console.error(e.response?.data || e.message);
    }
  };

  const handleCashOut = async () => {
    if (!handleValidated()) return;
    try {
      const requestBody = {
        wallet_id: selectWallet === "0" ? "default" : selectWallet,
        amount: selectAmount,
        agent_number: selectAgentAID,
      };

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/cashout`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      // console.log("Response Data :", res.data);
      toast.success("Cash out request sent successfully!");
      router.push("/dashboard/cash-out/cash-out-history");
    } catch (e) {
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
      }
      console.log(e.response.data);
    }
  };

  useEffect(() => {
    walletsData();
    fetchPaymentSettings();
    CashOutConfigData();
  }, []);

  return (
    <div>
      {/* Step Indicators */}
      <div className="max-w-lg mx-auto mb-4">
        <div className="flex justify-between">
          {[1, 2].map((s) => (
            <div key={s} className={`step-${s}`}>
              <div className="flex flex-col items-center">
                <div
                  className={`step-number w-8 h-8 flex justify-center items-center rounded-full 
                    ${step === s ? "bg-blue-600 text-white" : "bg-gray-200"}`}
                >
                  {s}
                </div>
                <div className="step-name">
                  {s === 1 ? "Cashout" : "Success"}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
        {step === 1 && (
          <div className="step-1-content">
            <div className="grid grid-cols-12 gap-5">
              {/* Agent id */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Agent AID
                </label>
                <input
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  type="number"
                  value={selectAgentAID}
                  onChange={(e) => setSelectAgentAID(e.target.value)}
                />
              </div>
              {/* Wallet */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Wallet
                </label>
                <select
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  disabled={walletsLoading}
                  value={selectWallet}
                  onChange={(e) => setSelectWallet(e.target.value)}
                >
                  <option value="">
                    {walletsLoading ? "Loading wallets..." : "Select Wallet"}
                  </option>
                  {wallets.map((wallet) => (
                    <option key={wallet.id} value={wallet.id}>
                      {wallet.full_name_balance}
                    </option>
                  ))}
                </select>
              </div>
              {/* Amount */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Amount
                </label>
                <input
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  type="number"
                  placeholder="Amount"
                  value={selectAmount}
                  onChange={(e) => setSelectAmount(e.target.value)}
                />
                {selectWallet && (
                  <p className="text-sm text-red-600 mt-1">
                    {
                      wallets.find((w) => w.id === Number(selectWallet))
                        ?.cashout_limit_text
                    }
                  </p>
                )}
              </div>
              {/* Next Button */}
              <div className="col-span-12 flex justify-end">
                <button
                  onClick={nextStep}
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="step-2-content">
            <p className="mb-4 text-xl font-medium border-b border-b-gray-500 pb-2">
              Review Details
            </p>
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Amount</h3>
                <p>
                  {Number(selectAmount).toFixed(
                    dynamicDecimals({
                      currencyCode: selectedWallet?.code,
                      siteCurrencyCode:
                        paymentSettings?.site_currency_code || "USD",
                      siteCurrencyDecimals:
                        paymentSettings?.site_currency_decimals || 2,
                      isCrypto: selectedWallet?.is_crypto || false,
                    })
                  )}{" "}
                  {selectedWallet?.code}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Wallet</h3>
                <p>{selectedWallet?.full_name}</p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Agent Account</h3>
                <p>{selectAgentAID}</p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Charge</h3>
                <p>
                  {Number(calculatedCharge).toFixed(
                    dynamicDecimals({
                      currencyCode: selectedWallet?.code,
                      siteCurrencyCode:
                        paymentSettings?.site_currency_code || "USD",
                      siteCurrencyDecimals:
                        paymentSettings?.site_currency_decimals || 2,
                      isCrypto: selectedWallet?.is_crypto || false,
                    })
                  )}{" "}
                  {selectedWallet?.code}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Total Amount</h3>
                <p>
                  {Number(totalAmount).toFixed(
                    dynamicDecimals({
                      currencyCode: selectedWallet?.code,
                      siteCurrencyCode:
                        paymentSettings?.site_currency_code || "USD",
                      siteCurrencyDecimals:
                        paymentSettings?.site_currency_decimals || 2,
                      isCrypto: selectedWallet?.is_crypto || false,
                    })
                  )}{" "}
                  {selectedWallet?.code}
                </p>
              </div>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded"
              >
                Back
              </button>
              <button
                onClick={handleCashOut}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Cash Out
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default page;
