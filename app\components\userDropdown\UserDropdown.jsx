"use client";
import axios from "axios";
import Cookies from "js-cookie";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const UserDropdown = () => {
  const [user, setUser] = useState("");
  const [userDropdown, setUserDropdown] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  const fetchUser = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/get`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setUser(res.data.data);
    } catch (e) {
      if (e.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
      console.log("User error:", e.response.data);
    }
  };

  const handleLogout = async () => {
    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/logout`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      Cookies.remove("token");
      router.push("/auth/login");
    } catch (e) {
      console.log("Logout error:", e.response.data);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);

  return (
    <div className="relative">
      <button
        className="flex gap-2 items-center"
        onClick={() => setUserDropdown(!userDropdown)}
      >
        <div className="block">
          {user?.avatar_path ? (
            <Image
              src={user.avatar_path}
              width={50}
              height={50}
              alt="User avatar"
              className="rounded-full w-[40px] h-[40px]"
            />
          ) : (
            <div className="w-[40px] h-[40px] rounded-full bg-gray-300 flex items-center justify-center">
              {/* maybe a fallback icon */}
            </div>
          )}
        </div>
        <span className="inline-flex flex-col items-start">
          <span className="text-lg font-semibold">{user.username}</span>
          <span className="text-sm text-gray-500">{user.email}</span>
        </span>
      </button>
      <div
        className={`mt-2 absolute right-0 w-48 z-10 ${
          userDropdown ? "hidden" : "block"
        }`}
      >
        <ul className="bg-gray-100 rounded-lg shadow-lg p-2">
          <div className="flex flex-col space-y-2">
            <Link
              href="/dashboard/settings/account-settings"
              className={`block px-4 py-2 rounded-md ${
                pathname === "/dashboard/settings/account-settings"
                  ? "bg-blue-600 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              Settings
            </Link>

            <Link
              href="/dashboard/settings/change-password"
              className={`block px-4 py-2 rounded-md ${
                pathname === "/dashboard/settings/change-password"
                  ? "bg-blue-600 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              Change Password
            </Link>

            <Link
              href="/dashboard/support"
              className={`block px-4 py-2 rounded-md ${
                pathname === "/dashboard/support"
                  ? "bg-blue-600 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              Support Ticket
            </Link>
          </div>
          <button
            className="px-4 py-2 text-red-500 block w-full text-start"
            onClick={handleLogout}
          >
            Logout
          </button>
        </ul>
      </div>
    </div>
  );
};

export default UserDropdown;
