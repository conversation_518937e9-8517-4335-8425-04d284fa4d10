@tailwind base;
@tailwind components;
@tailwind utilities;

.svg-container svg {
  width: 100%;
  height: 100%;
}

/*----------------------------------------*/
/* Referral tree styles
/*----------------------------------------*/
.referral-tree-main {
  @apply mx-auto my-0 mt-2.5;
}

.referral-tree-item {
  @apply relative px-4 py-0;
}
@media (max-width: 575px) {
  .referral-tree-item {
    @apply px-2 py-0;
  }
}
.referral-tree-card-inner {
  @apply relative before:absolute before:content-[""] before:z-[5] before:block before:h-[0.4375rem] before:pointer-events-none before:origin-[66%_66%] before:-translate-x-2/4 before:rotate-45 before:transition-all before:duration-[0.15s] before:ease-[ease-in-out] before:w-[0.4375rem] before:top-[-7px] before:border-b-2 before:border-b-[rgba(255,255,255,0.15)] before:border-solid;
}
.referral-tree-card-inner::before {
  inset-inline-start: calc(50% - 1px);
  border-inline-end: 2px solid rgba(255, 255, 255, 0.15);
  -webkit-transform-origin: 66% 66%;
  -webkit-transition: all 0.15s ease-in-out;
}

.referral-tree-card {
  @apply relative inline-flex flex-col gap-2.5 border min-w-[232px] px-3 py-2.5 rounded-xl rounded-md border-solid border-[rgba(255,255,255,0.15)];
  -webkit-border-radius: 0.75rem;
  -moz-border-radius: 0.75rem;
  -o-border-radius: 0.75rem;
  -ms-border-radius: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
}
.referral-tree-card.tree-parent {
  @apply min-w-max before:hidden after:absolute after:content-[""] after:w-1.5 after:h-1.5 after:-translate-x-2/4 after:top-[calc(100%_-_3px)] after:z-[5] after:rounded-[50%];
}
.referral-tree-card.tree-parent::after {
  background: rgba(255, 255, 255, 0.15);
  inset-inline-start: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
}
.referral-tree-card .thumb {
  @apply max-w-[3.75rem] mx-auto my-0;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}
.referral-tree-card .content .title {
  @apply text-base font-medium mb-0;
}
.referral-tree-card .content .info {
  @apply text-xs;
}
.td-referral-tree-wrapper {
  @apply max-w-max mt-[0.9375rem] mx-auto my-0;
}
.td-referral-tree-wrapper {
  @apply overflow-x-scroll mt-2.5;
}
.td-referral-tree {
  @apply pb-5 w-[1000px];
}
.td-referral-tree ul {
  @apply relative transition-all duration-[0.5s] flex pt-5;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
}
.td-referral-tree ul ul::before {
  @apply content-[""] absolute w-0 h-5 top-0;
  inset-inline-start: 50%;
  border-inline-start: 1px solid rgba(255, 255, 255, 0.15);
}
.light-theme .td-referral-tree ul ul::before {
  @apply border-[rgba(0,0,0,0.15)];
}
.td-referral-tree li {
  @apply float-left text-center list-none relative transition-all duration-[0.5s] pt-5 pb-0 px-[5px] only:pt-0 last:before:rounded-[0_0px_0_0] first:after:rounded-[0px_0_0_0];
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
}
.td-referral-tree li::after {
  inset-inline-end: auto;
  inset-inline-start: 50%;
  border-inline-start: 1px solid rgba(255, 255, 255, 0.15);
}
.td-referral-tree li:last-child::before {
  border-inline-end: 1px solid rgba(255, 255, 255, 0.15);
}
.td-referral-tree li::before,
.td-referral-tree li::after {
  @apply content-[""] absolute w-6/12 h-5 border-t-[rgba(255,255,255,0.15)] border-t border-solid top-0;
  inset-inline-end: 50%;
}
.td-referral-tree li:only-child::after,
.td-referral-tree li:only-child::before {
  @apply hidden;
}
.td-referral-tree li:first-child::before,
.td-referral-tree li:last-child::after {
  @apply border-0 border-none;
}
