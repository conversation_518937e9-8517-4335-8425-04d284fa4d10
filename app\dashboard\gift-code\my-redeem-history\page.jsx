"use client";

import { useSettings } from "@/context/SettingsContext";
import { dynamicDecimals, getSettingValue } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";

const page = () => {
  const [redeemHistory, setRedeemHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [paymentSettings, setPaymentSettings] = useState([]);
  const { settings } = useSettings();
  const siteCurrency = getSettingValue(settings, "site_currency");
  const siteCurrencyDecimals = getSettingValue(
    settings,
    "site_currency_decimals"
  );

  // for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState();
  const [perPage, setPerPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);

  const fetchRedeemHistory = async () => {
    setLoading(true);
    try {
      const params = searchQuery
        ? { code: searchQuery }
        : {
            page: currentPage,
            ...(perPage && { per_page: perPage }),
          };
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/gifts/redeem/history`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params,
        }
      );
      setRedeemHistory(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchRedeemHistory();
  };

  useEffect(() => {
    fetchRedeemHistory();
  }, [currentPage]);

  return (
    <div className="bg-white rounded-lg p-6">
      <div>
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4 bg-gray-100 rounded-lg p-3">
            <div>
              <input
                type="text"
                placeholder="Code"
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-700 text-sm">Per Page</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700"
                value={perPage}
                onChange={(e) => setPerPage(e.target.value)}
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
              onClick={handleSearch}
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                ></path>
              </svg>
              <span className="text-sm">Search</span>
            </button>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    SL No
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Gift Code
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Amount
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                    Claimed At
                  </th>
                </tr>
              </thead>
              {loading ? (
                <tbody>
                  <tr>
                    <td colSpan={5} className="py-4 px-6 text-center">
                      Loading...
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="divide-y divide-gray-200">
                  {redeemHistory?.gifts?.map((gift, index) => (
                    <tr
                      className="hover:bg-gray-50 transition-colors"
                      key={index}
                    >
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-700 font-mono">
                        {gift.code}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium text-orange-600">
                        {Number(gift.amount).toFixed(
                          dynamicDecimals({
                            currencyCode: gift.currency,
                            siteCurrencyCode: siteCurrency,
                            siteCurrencyDecimals: siteCurrencyDecimals,
                            isCrypto: gift?.is_crypto,
                          })
                        )}{" "}
                        {gift.currency}
                      </td>

                      <td className="px-6 py-4 text-sm">
                        {gift.is_redeemed ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                            Claimed
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Claimable
                          </span>
                        )}
                      </td>

                      <td className="px-6 py-4 text-sm text-gray-700">
                        {gift.claimed_at
                          ? new Date(gift.claimed_at).toLocaleString()
                          : "--"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
