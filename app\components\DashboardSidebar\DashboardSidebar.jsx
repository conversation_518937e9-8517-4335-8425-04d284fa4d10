"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

const DashboardSidebar = () => {
  const currentPath = usePathname();

  return (
    <aside className="w-64 bg-gray-900 text-white p-5">
      <h2 className="text-xl font-bold mb-6">My Dashboard</h2>
      <nav className="flex flex-col gap-2">
        <Link
          href="/dashboard"
          className={
            currentPath === "/dashboard"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Home
        </Link>

        <Link
          href="/dashboard/my-wallet"
          className={
            currentPath === "/dashboard/my-wallet"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          My Wallet
        </Link>

        <Link
          href="/dashboard/qr-code"
          className={
            currentPath === "/dashboard/qr-code"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          QR Code
        </Link>

        <Link
          href="/dashboard/add-money"
          className={
            currentPath.startsWith("/dashboard/add-money")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Add Money
        </Link>

        <Link
          href="/dashboard/transactions"
          className={
            currentPath === "/dashboard/transactions"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Transactions
        </Link>

        <Link
          href="/dashboard/make-payment"
          className={
            currentPath.startsWith("/dashboard/make-payment")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Make Payment
        </Link>

        <Link
          href="/dashboard/create-invoice"
          className={
            currentPath.startsWith("/dashboard/create-invoice")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Create Invoice
        </Link>

        <Link
          href="/dashboard/request"
          className={
            currentPath.startsWith("/dashboard/request")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Request Money
        </Link>

        <Link
          href="/dashboard/gift-code"
          className={
            currentPath.startsWith("/dashboard/gift-code")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Gift Code
        </Link>
        <Link
          href="/dashboard/cash-out"
          className={
            currentPath.startsWith("/dashboard/cash-out")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Cash Out
        </Link>

        <Link
          href="/dashboard/transfer"
          className={
            currentPath.startsWith("/dashboard/transfer")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Transfer
        </Link>

        <Link
          href="/dashboard/withdraw"
          className={
            currentPath.startsWith("/dashboard/withdraw")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Withdraw
        </Link>

        <Link
          href="/dashboard/exchange"
          className={
            currentPath.startsWith("/dashboard/exchange")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Exchange
        </Link>

        <Link
          href="/dashboard/referral"
          className={
            currentPath.startsWith("/dashboard/referral")
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Referral
        </Link>
      </nav>
    </aside>
  );
};

export default DashboardSidebar;
