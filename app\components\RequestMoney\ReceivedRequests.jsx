"use client";
import { useSettings } from "@/context/SettingsContext";
import { dynamicDecimals, getSettingValue } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";

const ReceivedRequests = () => {
  // fetch state
  const [receivedData, setReceivedData] = useState();

  // sate for site currency
  const { settings } = useSettings();
  const siteCurrency = getSettingValue(settings, "site_currency");
  const siteCurrencyDecimals = getSettingValue(
    settings,
    "site_currency_decimals"
  );

  // pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [lastPage, setLastPage] = useState(1);

  const router = useRouter();

  // Fetch receive request
  const fetchReceiveRequest = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/request-money/history`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            type: "received",
            page: currentPage,
            per_page: perPage,
          },
        }
      );

      setReceivedData(res.data.data);
      setLastPage(res.data.data.pagination.last_page);
    } catch (err) {
      console.error("API Error", err);
      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      } else if (err.response?.status === 422) {
        toast.error(err.response.data.message);
      } else {
        toast.error("Something went wrong, please try again");
      }
    }
  };

  // Handle Accept / Reject
  const sendReceiveRequest = async (id, action) => {
    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/request-money/${id}/action`,
        {}, // body (empty, since action is in query params)
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            action: action, // "accept" or "reject"
          },
        }
      );

      const statusText = action === "accept" ? "Accepted" : "Rejected";
      toast.success(res.data.message || `Request ${statusText} successfully`);

      fetchReceiveRequest(); // reload table
    } catch (err) {
      console.error("Update API Error", err);
      const statusText = action === "accept" ? "accept" : "reject";
      toast.error(
        err.response?.data?.message || `Failed to ${statusText} request`
      );

      if (err.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
    }
  };

  useEffect(() => {
    fetchReceiveRequest();
  }, [currentPage]);

  // Get status badge class
  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case "success":
        return "bg-green-900 text-green-300";
      case "pending":
        return "bg-yellow-900 text-yellow-300";
      case "rejected":
        return "bg-red-900 text-red-300";
      default:
        return "bg-gray-700 text-gray-300";
    }
  };

  // format amount based on is_crypto
  const formatAmount = (value, isCrypto) => {
    if (!value) return "0";
    const num = parseFloat(value);
    return isCrypto ? num.toFixed(8) : num.toFixed(2);
  };

  return (
    <>
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  SL No
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Request From
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Currency
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Amount
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Charge
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Final Amount
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Requested At
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Status
                </th>
                <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              {receivedData?.requests.map((item, index) => (
                <tr
                  key={item.id || index}
                  className="border-b border-gray-700 hover:bg-gray-750 transition-colors"
                >
                  {/* Serial Number (calculated per page) */}
                  <td className="py-4 px-4 text-gray-300">
                    {(currentPage - 1) * perPage + (index + 1)}
                  </td>
                  <td className="py-4 px-4 text-gray-300 font-mono text-sm">
                    {item.requester?.name || "Unknown"}
                  </td>
                  <td className="py-4 px-4 text-gray-300">{item.currency}</td>
                  <td className="py-4 px-4 text-gray-300">
                    {Number(item.amount).toFixed(
                      dynamicDecimals({
                        currencyCode: item.currency,
                        siteCurrencyCode: siteCurrency,
                        siteCurrencyDecimals: siteCurrencyDecimals,
                        isCrypto: item?.is_crypto,
                      })
                    )}{" "}
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    {Number(item.charge).toFixed(
                      dynamicDecimals({
                        currencyCode: item.currency,
                        siteCurrencyCode: siteCurrency,
                        siteCurrencyDecimals: siteCurrencyDecimals,
                        isCrypto: item?.is_crypto,
                      })
                    )}{" "}
                    {item.currency}
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    {formatAmount(item.final_amount, item.is_crypto)}{" "}
                    {item.currency}
                  </td>
                  <td className="py-4 px-4 text-gray-300">{item.created_at}</td>
                  <td className="py-4 px-4 text-gray-300">
                    <span
                      className={`px-2 py-1 rounded-md text-xs ${getStatusClass(
                        item.status
                      )}`}
                    >
                      {item.status}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-gray-300">
                    {item.can_action ? (
                      <div className="flex gap-2">
                        <button
                          onClick={() => sendReceiveRequest(item.id, "accept")}
                          className="px-3 py-1 rounded bg-green-600 hover:bg-green-700 text-white text-sm"
                        >
                          Accept
                        </button>
                        <button
                          onClick={() => sendReceiveRequest(item.id, "reject")}
                          className="px-3 py-1 rounded bg-red-600 hover:bg-red-700 text-white text-sm"
                        >
                          Reject
                        </button>
                      </div>
                    ) : (
                      <span className="text-gray-500 text-sm">—</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="p-4">
          {receivedData?.pagination && (
            <div className="flex items-center">
              <nav className="flex items-center space-x-1">
                {/* Prev Button */}
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md 
                    ${
                      currentPage === 1
                        ? "text-gray-500 cursor-not-allowed"
                        : "text-gray-300"
                    }`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 19l-7-7 7-7"
                    ></path>
                  </svg>
                </button>

                {/* Page Numbers */}
                {[...Array(lastPage)].map((_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-8 h-8 flex items-center justify-center border border-gray-700 rounded-md 
                        ${
                          currentPage === pageNum
                            ? "bg-blue-500 text-white"
                            : "text-gray-300"
                        }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                {/* Next Button */}
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, lastPage))
                  }
                  disabled={currentPage === lastPage}
                  className={`w-8 h-8 flex items-center border border-gray-700 justify-center rounded-md 
                    ${
                      currentPage === lastPage
                        ? "text-gray-500 cursor-not-allowed"
                        : "text-gray-300"
                    }`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </button>
              </nav>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
export default ReceivedRequests;
